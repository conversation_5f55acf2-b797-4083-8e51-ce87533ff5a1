package com.moego.server.business.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BusinessPayrollSettingDTO {

    private Integer id;
    private Integer businessId;
    private Long companyId;

    @Schema(description = "business设置的split tips method: 1-by service, 2-by equally")
    private Byte splitTipsMethod;

    @Schema(description = "新 payroll setting 白名单开关")
    private Boolean newPayrollEnable;

    @Schema(description = "service commission 计算基数: 1-actual payment, 2-finish appointment")
    private Byte serviceCommissionBased;

    private Integer createTime;
    private Integer updateTime;
}
