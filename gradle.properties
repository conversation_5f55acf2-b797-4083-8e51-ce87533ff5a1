group=com.moego
artifact=moego-server-grooming
version=1.0.0

# Spring related
springBootVersion=3.1.4
springCloudVersion=2022.0.4
springDependencyManagementVersion=1.1.3

# https://github.com/awspring/spring-cloud-aws/blob/v3.2.1/spring-cloud-aws-dependencies/pom.xml
# ??????? lib-aws??????????????? springCloudAWS ?????????springCloudAWS ????????
# ??? springCloudAWS 3.2.1 ??? awssdk ??
awsSdkVersion=2.25.70
# https://github.com/awspring/spring-cloud-aws?tab=readme-ov-file#compatibility-with-spring-project-versions
springCloudAWSVersion=3.0.5

# https://github.com/mybatis/spring-boot-starter
mybatisBootStarterVersion=3.0.2
mybatisGeneratorCoreVersion=1.4.2
# https://plugins.gradle.org/plugin/com.qqviaja.gradle.MybatisGenerator
mybatisGeneratorGradlePlugin=2.5
pagehelperSpringBootStarterVersion=2.0.0

grpcVersion=1.58.0
mapstructVersion=1.5.5.Final

# Code quality
spotlessVersion=6.22.0
spotbugsVersion=5.1.3

org.gradle.jvmargs=-Xmx4g
org.gradle.daemon=true
org.gradle.caching=true
org.gradle.cache.lockTimeout=1200000
