group=com.moego
artifact=moego-server-customer
version=1.0.0

# Spring related
springBootVersion=3.1.4
springCloudVersion=2022.0.4
springDependencyManagementVersion=1.1.3

# https://github.com/awspring/spring-cloud-aws/blob/v3.2.1/spring-cloud-aws-dependencies/pom.xml
# 这个服务依赖了 lib-aws（建议是不要用），使用的版本和 springCloudAWS 使用的版本有冲突（springCloudAWS 使用的版本较低）
# 这里用 springCloudAWS 3.2.1 对应的 awssdk 版本
awsSdkVersion=2.25.70
# https://github.com/awspring/spring-cloud-aws?tab=readme-ov-file#compatibility-with-spring-project-versions
springCloudAWSVersion=3.0.5

mybatisBootStarterVersion=3.0.2
mybatisGeneratorCoreVersion=1.4.2
# https://plugins.gradle.org/plugin/com.qqviaja.gradle.MybatisGenerator
mybatisGeneratorGradlePlugin=2.5
pagehelperSpringBootStarterVersion=2.0.0
jacocoToCoberturaPlugin=1.2.0

grpcVersion=1.58.0
mapstructVersion=1.5.5.Final

# Code quality
spotlessVersion=6.22.0
spotbugsVersion=5.1.3

org.gradle.jvmargs=-Xmx3g
org.gradle.caching=true
org.gradle.cache.lockTimeout=1200000
