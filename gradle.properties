group=com.moego

# spring
springBootVersion=3.1.4
springCloudVersion=2022.0.4
# https://github.com/spring-gradle-plugins/dependency-management-plugin
springDependencyManagementVersion=1.1.6
springCloudAlibabaVersion=2022.0.0.2
nacosVersion=2.5.1

grpcVersion=1.58.0

# https://plugins.gradle.org/plugin/com.qqviaja.gradle.MybatisGenerator
mybatisGeneratorGradlePlugin=2.5
# https://github.com/mybatis/generator
mybatisGeneratorCoreVersion=1.4.2
# https://github.com/mybatis/spring-boot-starter
mybatisBootStarterVersion=3.0.3
# https://github.com/mybatis/mybatis-dynamic-sql
mybatisDynamicSqlVersion=1.5.2

# https://github.com/mapstruct/mapstruct
mapstructVersion=1.6.3
# https://central.sonatype.com/artifact/org.projectlombok/lombok-mapstruct-binding
lombokMapstructBindingVersion=0.2.0

# code quality
# https://plugins.gradle.org/plugin/com.diffplug.gradle.spotless
spotlessVersion=6.25.0
# https://plugins.gradle.org/plugin/com.github.spotbugs
spotbugsVersion=5.2.2

org.gradle.jvmargs=-Xmx4g
org.gradle.caching=true
