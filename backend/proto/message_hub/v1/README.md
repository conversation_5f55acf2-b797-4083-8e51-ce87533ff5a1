# Message Hub API v1 文档

本文档旨在为开发者提供 Message Hub v1 版本的 API 参考。其核心功能是发送短信与语音通话，并通过异步回调机制，向调用方反馈消息的投递状态及用户的回复。

## 设计理念

Message Hub 允许客户端发送消息，并通过一个灵活的回调机制接收后续的事件通知。客户端在每次发送请求时，都可以独立指定该条消息的回调方式。

### 系统架构图

下图展示了客户端、Message Hub 以及回调机制之间的关系：

```mermaid
graph TD
    subgraph "Client System"
        ClientApp[Client Application]
        CallbackServer[gRPC CallbackService Impl]
        KafkaConsumer[Client's Kafka Consumer]
    end

    subgraph "Message Hub"
        MessageHub[MessageHubService]
    end

    subgraph "Downstream Services"
        Provider[Third-party Provider]
    end

    subgraph "Infrastructure"
        Kafka[Kafka Broker]
    end

    ClientApp -- "1. Send Message" --> MessageHub
    MessageHub -- "2. Ack with Message ID" --> ClientApp

    MessageHub -- "3. Forward to Provider" --> Provider
    Provider -- "4. Receive Async Events (State Update, Reply)" --> MessageHub

    MessageHub -- "5a. Push via gRPC" --> CallbackServer
    MessageHub -- "5b. Push to Kafka" --> Kafka
    Kafka -- "6. Consume from Topic" --> KafkaConsumer

    style ClientApp fill:#cce5ff,stroke:#333,stroke-width:2px
    style MessageHub fill:#d5e8d4,stroke:#333,stroke-width:2px
    style Provider fill:#fff2cc,stroke:#333,stroke-width:2px
```

### 回调机制

目前支持两种回调方式：

1.  **gRPC 回调**：客户端需自行实现并暴露 `CallbackService` 中定义的 gRPC 接口。Message Hub 会以客户端的身份，主动调用这些接口来推送事件。
2.  **Kafka 回调**：Message Hub 将事件消息推送到客户端指定的 Kafka Topic 中。

### 消息类型

支持以下两种消息类型：

1.  **短信 (SMS)**：发送标准的文本消息，并支持投递状态的跟踪。请注意，由于用户的短信回复难以精确关联到某一次特定的发送请求，因此这类回复并不会通过 `CallbackService` 进行处理。作为替代方案，Message Hub 会将所有接收到的用户短信统一投递到一个公共的 Kafka Topic，供各业务方自行消费和处理。
2.  **语音通话 (Voice Call)**：向目标号码发起通话并播报指定的文本内容（TTS）。此消息类型不仅支持投递状态跟踪，还能捕获用户的按键回复（例如，“若同意请按 1”）。

### 工作流程

1.  客户端首先构建一个 `SendMessageRequest` 请求，并在其中选择 `sms` 或 `call` 作为本次发送的载体 (Payload)。
2.  在载体中，客户端根据业务需求，为“状态更新”和“用户回复”（仅语音通话支持）配置所需的回调方式（gRPC 或 Kafka）。
3.  客户端调用 `SendMessage` 接口发起请求。
4.  `SendMessage` 接口会立即返回一个包含唯一 `message_id` 和初始受理状态的同步响应。
5.  最后，客户端在自己选择的渠道上（gRPC 服务或 Kafka 消费者）异步等待后续的事件通知即可。

### 工作流程时序图

以下时序图分别展示了发送短信 (SMS) 和语音通话 (Voice Call) 的完整流程。

#### 短信 (SMS) 工作流程

```mermaid
sequenceDiagram
    autonumber
    participant Client
    participant Message Hub
    participant Third-party Provider (Twilio)
    participant Client's gRPC Server
    participant Client's Kafka Consumer

    Client->>+Message Hub: SendMessage(SmsPayload)
    Message Hub-->>-Client: Sync Response (message_id, initial_state)

    Message Hub->>+Third-party Provider (Twilio): Send SMS Request
    Third-party Provider (Twilio)-->>-Message Hub: Ack

    Note over Third-party Provider (Twilio), Message Hub: Later, delivery status is updated...

    Third-party Provider (Twilio)->>+Message Hub: Async State Update (e.g., delivered)
    Message Hub-->>-Third-party Provider (Twilio): Ack

    alt Optional: State Callback via gRPC
        Message Hub->>+Client's gRPC Server: HandleState(update)
        Client's gRPC Server-->>-Message Hub: Ack
    else Optional: State Callback via Kafka
        Message Hub->>Client's Kafka Consumer: Publishes state event
    end
```

#### 语音通话 (Voice Call) 工作流程

```mermaid
sequenceDiagram
    autonumber
    participant Client
    participant Message Hub
    participant Third-party Provider (Twilio)
    participant Client's gRPC Server
    participant Client's Kafka Consumer

    Client->>+Message Hub: SendMessage(CallPayload)
    Message Hub-->>-Client: Sync Response (message_id, initial_state)

    Message Hub->>+Third-party Provider (Twilio): Initiate Call Request
    Third-party Provider (Twilio)-->>-Message Hub: Ack

    Note over Third-party Provider (Twilio), Message Hub: Later, async events occur independently...

    Third-party Provider (Twilio)->>+Message Hub: Async State Update (e.g., delivered)
    Message Hub-->>-Third-party Provider (Twilio): Ack
    alt Optional: State Callback via gRPC
        Message Hub->>+Client's gRPC Server: HandleState(update)
        Client's gRPC Server-->>-Message Hub: Ack
    else Optional: State Callback via Kafka
        Message Hub->>Client's Kafka Consumer: Publishes state event
    end

    Third-party Provider (Twilio)->>+Message Hub: Async User Reply (e.g., DTMF tone)
    Message Hub-->>-Third-party Provider (Twilio): Ack
    alt Optional: Reply Callback via gRPC
        Message Hub->>+Client's gRPC Server: HandleReply(reply)
        Client's gRPC Server-->>-Message Hub: Ack
    else Optional: Reply Callback via Kafka
        Message Hub->>Client's Kafka Consumer: Publishes reply event
    end
```

---

## `MessageHubService`

### RPC 方法

#### `SendMessage`

发送单条短信或语音通话消息。

```protobuf
rpc SendMessage(SendMessageRequest) returns (SendMessageResponse);
```

**使用说明：**

调用此接口时，请求体中必须包含 `sms` 或 `call` 两者中的任意一个作为消息载体。

**请求: `SendMessageRequest`**

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `sms` | `SendSmsPayload` | 短信消息载体。 |
| `call` | `SendCallPayload` | 语音通话消息载体。 |

**响应: `SendMessageResponse`**

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `message_id` | `string` | 由 Message Hub 生成的消息唯一标识符，用于追踪消息。 |
| `state` | `SendState` | 服务受理此请求的初始状态。 |
| `error_code` | `int32` | 请求受理失败时的错误码，`0` 表示成功。 |
| `error_message` | `string` | 请求受理失败时的错误信息。 |

---

### 消息载体与回调配置

#### `SendSmsPayload`

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `region_code` | `string` | ISO 3166-1 alpha-2 国家代码，用于指定 `sender` 和 `recipient` 所在的国家。 |
| `sender` | `google.type.PhoneNumber` | 发送方号码（可选）。B2C 场景下，应设为 B 端的号码；平台发送场景下，无需指定，系统将自动使用平台号码。 |
| `recipient` | `google.type.PhoneNumber` | 接收方号码。 |
| `content` | `string` | 短信的文本内容。 |
| `state_callback` | `Callback` | 用于接收此短信投递状态更新的回调配置（可选）。 |

#### `SendCallPayload`

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `region_code` | `string` | ISO 3166-1 alpha-2 国家代码，用于指定 `sender` 和 `recipient` 所在的国家。 |
| `sender` | `google.type.PhoneNumber` | 发送方号码（可选）。B2C 场景下，应设为 B 端的号码；平台发送场景下，无需指定，系统将自动使用平台号码。 |
| `recipient` | `google.type.PhoneNumber` | 接收方号码。 |
| `content` | `string` | 通话中需要播报的文本内容（TTS）。 |
| `state_callback` | `Callback` | 用于接收此通话投递状态更新的回调配置（可选）。 |
| `reply_callback` | `Callback` | 用于接收用户按键回复的回调配置（可选）。 |

#### `Callback` 对象

此对象用于定义具体的回调方式。

| 字段 | 类型 | 描述 |
| :--- | :--- |:---|
| `kafka` | `KafkaCallback` | 指定使用 Kafka 作为回调方式。 |
| `grpc` | `GrpcCallback` | 指定使用 gRPC 作为回调方式。 |

**`KafkaCallback`**

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `topic` | `string` | 用于接收回调消息的 Kafka Topic。 |

**`GrpcCallback`**

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `server` | `string` | 客户端实现的 gRPC 服务地址 (例如, `my-service:9090`)。 |

---

## `CallbackService`

当客户端选择 gRPC 作为回调方式时，需要自行实现 `CallbackService` 中定义的接口。它包含两个核心方法，分别用于处理状态更新与用户回复。

如果在发送消息时指定了 `state_callback`，Message Hub 会调用 `HandleState` 接口；如果指定了 `reply_callback`，则会调用 `HandleReply` 接口。

### RPC 方法

#### `HandleState`

此接口用于接收消息投递状态的异步通知。选择 gRPC 回调且需要状态通知的客户端必须实现此方法。

```protobuf
rpc HandleState(HandleStateRequest) returns (HandleStateResponse);
```

**请求: `HandleStateRequest`**

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `message_id` | `string` | 与发送请求时返回的 `message_id` 一致。 |
| `state` | `SendState` | 消息的最新投递状态。 |
| `error_code` | `int32` | 发送失败时的错误码。 |
| `error_message` | `string` | 发送失败时的错误信息。 |

**响应: `HandleStateResponse`**

此响应为空，客户端实现时返回空响应即可，表示确认收到。

---

#### `HandleReply`

此接口用于接收用户的回复（目前仅语音通话支持，用户可回复按键内容）。需要接收此类回复的客户端必须实现此方法。

```protobuf
rpc HandleReply(HandleReplyRequest) returns (HandleReplyResponse);
```

**请求: `HandleReplyRequest`**

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `message_id` | `string` | 与发送请求时返回的 `message_id` 一致。 |
| `reply` | `string` | 用户的按键回复内容。 |

**响应: `HandleReplyResponse`**

此响应为空，客户端实现时返回空响应即可，表示确认收到。

---

## 数据结构

### `SendState`

消息发送状态枚举。

| 值 | 描述                        |
| :--- |:--------------------------|
| `SEND_STATE_UNSPECIFIED` | 未指定状态。                    |
| `SEND_STATE_SENT` | 消息已成功从本服务发出，但下游运营商尚未确认送达。 |
| `SEND_STATE_FAILED` | 消息发送失败。                   |
| `SEND_STATE_DELIVERED` | 下游运营商已确认消息成功送达接收方。        |