// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/message_hub/v1/message_hub_service.proto

package messagehubpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	phone_number "google.golang.org/genproto/googleapis/type/phone_number"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 996400
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		996400: "ERR_CODE_UNSPECIFIED",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":          0,
		"ERR_CODE_UNSPECIFIED": 996400,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_message_hub_v1_message_hub_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_message_hub_v1_message_hub_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_message_hub_service_proto_rawDescGZIP(), []int{0}
}

// SendMessageRequest
type SendMessageRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消息的有效负载。每个请求只能发送一种类型。
	//
	// Types that are valid to be assigned to Payload:
	//
	//	*SendMessageRequest_Sms
	//	*SendMessageRequest_Call
	Payload       isSendMessageRequest_Payload `protobuf_oneof:"payload"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageRequest) Reset() {
	*x = SendMessageRequest{}
	mi := &file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageRequest) ProtoMessage() {}

func (x *SendMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageRequest.ProtoReflect.Descriptor instead.
func (*SendMessageRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_message_hub_service_proto_rawDescGZIP(), []int{0}
}

func (x *SendMessageRequest) GetPayload() isSendMessageRequest_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *SendMessageRequest) GetSms() *SendSmsPayload {
	if x != nil {
		if x, ok := x.Payload.(*SendMessageRequest_Sms); ok {
			return x.Sms
		}
	}
	return nil
}

func (x *SendMessageRequest) GetCall() *SendCallPayload {
	if x != nil {
		if x, ok := x.Payload.(*SendMessageRequest_Call); ok {
			return x.Call
		}
	}
	return nil
}

type isSendMessageRequest_Payload interface {
	isSendMessageRequest_Payload()
}

type SendMessageRequest_Sms struct {
	// 短信消息。
	Sms *SendSmsPayload `protobuf:"bytes,1,opt,name=sms,proto3,oneof"`
}

type SendMessageRequest_Call struct {
	// 语音通话消息。
	Call *SendCallPayload `protobuf:"bytes,2,opt,name=call,proto3,oneof"`
}

func (*SendMessageRequest_Sms) isSendMessageRequest_Payload() {}

func (*SendMessageRequest_Call) isSendMessageRequest_Payload() {}

// SendSmsPayload
type SendSmsPayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ISO 3166-1 alpha-2 标准国家代码。
	// 表示 sender 和 recipient 所在的国家。目前不支持跨国发送。
	RegionCode string `protobuf:"bytes,1,opt,name=region_code,json=regionCode,proto3" json:"region_code,omitempty"`
	// 发送方的电话号码。
	// 对于 B 端发送给 C 端的场景，需要设置 B 端的手机号作为 sender。
	// 对于 MoeGo 平台对外发送的场景，不需要设置 sender，服务端会自行使用平台的号码发送。
	Sender *phone_number.PhoneNumber `protobuf:"bytes,2,opt,name=sender,proto3,oneof" json:"sender,omitempty"`
	// 接收方的电话号码。
	Recipient *phone_number.PhoneNumber `protobuf:"bytes,3,opt,name=recipient,proto3" json:"recipient,omitempty"`
	// 短信的内容。
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	// 用于接收此短信状态变化的回调。
	StateCallback *Callback `protobuf:"bytes,5,opt,name=state_callback,json=stateCallback,proto3,oneof" json:"state_callback,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSmsPayload) Reset() {
	*x = SendSmsPayload{}
	mi := &file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSmsPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsPayload) ProtoMessage() {}

func (x *SendSmsPayload) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsPayload.ProtoReflect.Descriptor instead.
func (*SendSmsPayload) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_message_hub_service_proto_rawDescGZIP(), []int{1}
}

func (x *SendSmsPayload) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

func (x *SendSmsPayload) GetSender() *phone_number.PhoneNumber {
	if x != nil {
		return x.Sender
	}
	return nil
}

func (x *SendSmsPayload) GetRecipient() *phone_number.PhoneNumber {
	if x != nil {
		return x.Recipient
	}
	return nil
}

func (x *SendSmsPayload) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SendSmsPayload) GetStateCallback() *Callback {
	if x != nil {
		return x.StateCallback
	}
	return nil
}

// SendCallPayload
type SendCallPayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ISO 3166-1 alpha-2 标准国家代码。
	// 表示 sender 和 recipient 所在的国家。目前不支持跨国发送。
	RegionCode string `protobuf:"bytes,1,opt,name=region_code,json=regionCode,proto3" json:"region_code,omitempty"`
	// 发送方的电话号码。
	// 对于 B 端发送给 C 端的场景，需要设置 B 端的手机号作为 sender。
	// 对于 MoeGo 平台对外发送的场景，不需要设置 sender，服务端会自行使用平台的号码发送。
	Sender *phone_number.PhoneNumber `protobuf:"bytes,2,opt,name=sender,proto3,oneof" json:"sender,omitempty"`
	// 接收方的电话号码。
	Recipient *phone_number.PhoneNumber `protobuf:"bytes,3,opt,name=recipient,proto3" json:"recipient,omitempty"`
	// 通话中要播放的内容。
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	// 用于接收此通话状态变化的回调。
	StateCallback *Callback `protobuf:"bytes,5,opt,name=state_callback,json=stateCallback,proto3,oneof" json:"state_callback,omitempty"`
	// 用于接收来自接收方的数字回复的回调。
	ReplyCallback *Callback `protobuf:"bytes,6,opt,name=reply_callback,json=replyCallback,proto3,oneof" json:"reply_callback,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendCallPayload) Reset() {
	*x = SendCallPayload{}
	mi := &file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendCallPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendCallPayload) ProtoMessage() {}

func (x *SendCallPayload) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendCallPayload.ProtoReflect.Descriptor instead.
func (*SendCallPayload) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_message_hub_service_proto_rawDescGZIP(), []int{2}
}

func (x *SendCallPayload) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

func (x *SendCallPayload) GetSender() *phone_number.PhoneNumber {
	if x != nil {
		return x.Sender
	}
	return nil
}

func (x *SendCallPayload) GetRecipient() *phone_number.PhoneNumber {
	if x != nil {
		return x.Recipient
	}
	return nil
}

func (x *SendCallPayload) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SendCallPayload) GetStateCallback() *Callback {
	if x != nil {
		return x.StateCallback
	}
	return nil
}

func (x *SendCallPayload) GetReplyCallback() *Callback {
	if x != nil {
		return x.ReplyCallback
	}
	return nil
}

// SendMessageResponse
type SendMessageResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 由 Message Hub 生成的消息的唯一标识符。
	MessageId string `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// 消息接收的初始状态。
	// (-- api-linter: core::0216::state-field-output-only=disabled
	//
	//	aip.dev/not-precedent: This field is in a response message,
	//	which is inherently output-only. --)
	State SendState `protobuf:"varint,2,opt,name=state,proto3,enum=backend.proto.message_hub.v1.SendState" json:"state,omitempty"`
	// 如果消息发送失败，则为错误代码。
	ErrorCode int32 `protobuf:"varint,3,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// 如果消息发送失败，则为错误信息。
	ErrorMessage  string `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageResponse) Reset() {
	*x = SendMessageResponse{}
	mi := &file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageResponse) ProtoMessage() {}

func (x *SendMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageResponse.ProtoReflect.Descriptor instead.
func (*SendMessageResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_message_hub_v1_message_hub_service_proto_rawDescGZIP(), []int{3}
}

func (x *SendMessageResponse) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *SendMessageResponse) GetState() SendState {
	if x != nil {
		return x.State
	}
	return SendState_SEND_STATE_UNSPECIFIED
}

func (x *SendMessageResponse) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *SendMessageResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

var File_backend_proto_message_hub_v1_message_hub_service_proto protoreflect.FileDescriptor

const file_backend_proto_message_hub_v1_message_hub_service_proto_rawDesc = "" +
	"\n" +
	"6backend/proto/message_hub/v1/message_hub_service.proto\x12\x1cbackend.proto.message_hub.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1egoogle/type/phone_number.proto\x1a+backend/proto/message_hub/v1/callback.proto\x1a-backend/proto/message_hub/v1/send_state.proto\"\xad\x01\n" +
	"\x12SendMessageRequest\x12@\n" +
	"\x03sms\x18\x01 \x01(\v2,.backend.proto.message_hub.v1.SendSmsPayloadH\x00R\x03sms\x12C\n" +
	"\x04call\x18\x02 \x01(\v2-.backend.proto.message_hub.v1.SendCallPayloadH\x00R\x04callB\x10\n" +
	"\apayload\x12\x05\xbaH\x02\b\x01\"\xbf\x02\n" +
	"\x0eSendSmsPayload\x12)\n" +
	"\vregion_code\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x98\x01\x02R\n" +
	"regionCode\x125\n" +
	"\x06sender\x18\x02 \x01(\v2\x18.google.type.PhoneNumberH\x00R\x06sender\x88\x01\x01\x126\n" +
	"\trecipient\x18\x03 \x01(\v2\x18.google.type.PhoneNumberR\trecipient\x12!\n" +
	"\acontent\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\acontent\x12R\n" +
	"\x0estate_callback\x18\x05 \x01(\v2&.backend.proto.message_hub.v1.CallbackH\x01R\rstateCallback\x88\x01\x01B\t\n" +
	"\a_senderB\x11\n" +
	"\x0f_state_callback\"\xa7\x03\n" +
	"\x0fSendCallPayload\x12)\n" +
	"\vregion_code\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x98\x01\x02R\n" +
	"regionCode\x125\n" +
	"\x06sender\x18\x02 \x01(\v2\x18.google.type.PhoneNumberH\x00R\x06sender\x88\x01\x01\x126\n" +
	"\trecipient\x18\x03 \x01(\v2\x18.google.type.PhoneNumberR\trecipient\x12!\n" +
	"\acontent\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\acontent\x12R\n" +
	"\x0estate_callback\x18\x05 \x01(\v2&.backend.proto.message_hub.v1.CallbackH\x01R\rstateCallback\x88\x01\x01\x12R\n" +
	"\x0ereply_callback\x18\x06 \x01(\v2&.backend.proto.message_hub.v1.CallbackH\x02R\rreplyCallback\x88\x01\x01B\t\n" +
	"\a_senderB\x11\n" +
	"\x0f_state_callbackB\x11\n" +
	"\x0f_reply_callback\"\xb7\x01\n" +
	"\x13SendMessageResponse\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x12=\n" +
	"\x05state\x18\x02 \x01(\x0e2'.backend.proto.message_hub.v1.SendStateR\x05state\x12\x1d\n" +
	"\n" +
	"error_code\x18\x03 \x01(\x05R\terrorCode\x12#\n" +
	"\rerror_message\x18\x04 \x01(\tR\ferrorMessage*6\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10\xb0\xe8<2\x87\x01\n" +
	"\x11MessageHubService\x12r\n" +
	"\vSendMessage\x120.backend.proto.message_hub.v1.SendMessageRequest\x1a1.backend.proto.message_hub.v1.SendMessageResponseBs\n" +
	"&com.moego.backend.proto.message_hub.v1P\x01ZGgithub.com/MoeGolibrary/moego/backend/proto/message_hub/v1;messagehubpbb\x06proto3"

var (
	file_backend_proto_message_hub_v1_message_hub_service_proto_rawDescOnce sync.Once
	file_backend_proto_message_hub_v1_message_hub_service_proto_rawDescData []byte
)

func file_backend_proto_message_hub_v1_message_hub_service_proto_rawDescGZIP() []byte {
	file_backend_proto_message_hub_v1_message_hub_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_message_hub_v1_message_hub_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_message_hub_service_proto_rawDesc), len(file_backend_proto_message_hub_v1_message_hub_service_proto_rawDesc)))
	})
	return file_backend_proto_message_hub_v1_message_hub_service_proto_rawDescData
}

var file_backend_proto_message_hub_v1_message_hub_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_backend_proto_message_hub_v1_message_hub_service_proto_goTypes = []any{
	(ErrCode)(0),                     // 0: backend.proto.message_hub.v1.ErrCode
	(*SendMessageRequest)(nil),       // 1: backend.proto.message_hub.v1.SendMessageRequest
	(*SendSmsPayload)(nil),           // 2: backend.proto.message_hub.v1.SendSmsPayload
	(*SendCallPayload)(nil),          // 3: backend.proto.message_hub.v1.SendCallPayload
	(*SendMessageResponse)(nil),      // 4: backend.proto.message_hub.v1.SendMessageResponse
	(*phone_number.PhoneNumber)(nil), // 5: google.type.PhoneNumber
	(*Callback)(nil),                 // 6: backend.proto.message_hub.v1.Callback
	(SendState)(0),                   // 7: backend.proto.message_hub.v1.SendState
}
var file_backend_proto_message_hub_v1_message_hub_service_proto_depIdxs = []int32{
	2,  // 0: backend.proto.message_hub.v1.SendMessageRequest.sms:type_name -> backend.proto.message_hub.v1.SendSmsPayload
	3,  // 1: backend.proto.message_hub.v1.SendMessageRequest.call:type_name -> backend.proto.message_hub.v1.SendCallPayload
	5,  // 2: backend.proto.message_hub.v1.SendSmsPayload.sender:type_name -> google.type.PhoneNumber
	5,  // 3: backend.proto.message_hub.v1.SendSmsPayload.recipient:type_name -> google.type.PhoneNumber
	6,  // 4: backend.proto.message_hub.v1.SendSmsPayload.state_callback:type_name -> backend.proto.message_hub.v1.Callback
	5,  // 5: backend.proto.message_hub.v1.SendCallPayload.sender:type_name -> google.type.PhoneNumber
	5,  // 6: backend.proto.message_hub.v1.SendCallPayload.recipient:type_name -> google.type.PhoneNumber
	6,  // 7: backend.proto.message_hub.v1.SendCallPayload.state_callback:type_name -> backend.proto.message_hub.v1.Callback
	6,  // 8: backend.proto.message_hub.v1.SendCallPayload.reply_callback:type_name -> backend.proto.message_hub.v1.Callback
	7,  // 9: backend.proto.message_hub.v1.SendMessageResponse.state:type_name -> backend.proto.message_hub.v1.SendState
	1,  // 10: backend.proto.message_hub.v1.MessageHubService.SendMessage:input_type -> backend.proto.message_hub.v1.SendMessageRequest
	4,  // 11: backend.proto.message_hub.v1.MessageHubService.SendMessage:output_type -> backend.proto.message_hub.v1.SendMessageResponse
	11, // [11:12] is the sub-list for method output_type
	10, // [10:11] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_backend_proto_message_hub_v1_message_hub_service_proto_init() }
func file_backend_proto_message_hub_v1_message_hub_service_proto_init() {
	if File_backend_proto_message_hub_v1_message_hub_service_proto != nil {
		return
	}
	file_backend_proto_message_hub_v1_callback_proto_init()
	file_backend_proto_message_hub_v1_send_state_proto_init()
	file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes[0].OneofWrappers = []any{
		(*SendMessageRequest_Sms)(nil),
		(*SendMessageRequest_Call)(nil),
	}
	file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes[1].OneofWrappers = []any{}
	file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_message_hub_v1_message_hub_service_proto_rawDesc), len(file_backend_proto_message_hub_v1_message_hub_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_message_hub_v1_message_hub_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_message_hub_v1_message_hub_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_message_hub_v1_message_hub_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_message_hub_v1_message_hub_service_proto_msgTypes,
	}.Build()
	File_backend_proto_message_hub_v1_message_hub_service_proto = out.File
	file_backend_proto_message_hub_v1_message_hub_service_proto_goTypes = nil
	file_backend_proto_message_hub_v1_message_hub_service_proto_depIdxs = nil
}
