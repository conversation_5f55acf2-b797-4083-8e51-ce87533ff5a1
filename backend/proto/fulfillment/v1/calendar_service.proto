syntax = "proto3";

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: lodging package structure is appropriate for this domain --)
package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "backend/proto/fulfillment/v1/common.proto";
import "buf/validate/validate.proto";
import "backend/proto/fulfillment/v1/calendar.proto";
import "backend/proto/offering/v1/care_type.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// CalendarService 住宿服务
service CalendarService {
  // GetLodgingCalendar 获取住宿日历信息
  // (-- api-linter: core::0131::response-message-name=disabled
  //     aip.dev/not-precedent: GetLodgingCalendarResponse is appropriate for this use case --)
  rpc GetLodgingCalendar(GetLodgingCalendarRequest) returns (GetLodgingCalendarResponse);
}

// GetLodgingCalendarRequest 获取住宿日历请求
message GetLodgingCalendarRequest {
    // 公司ID
    int64 company_id = 1 [(buf.validate.field).int64.gt = 0];
    // 商家ID
    int64 business_id = 2 [(buf.validate.field).int64.gt = 0];
    // 查询开始时间
    google.protobuf.Timestamp start_time = 3;
    // 查询结束时间
    google.protobuf.Timestamp end_time = 4;
    // 过滤条件
    LodgingFilter filter = 5;
}

// GetLodgingCalendarResponse 获取住宿日历响应
message GetLodgingCalendarResponse {
  // 住宿信息列表
  // (-- api-linter: core::0140::lower-snake=disabled
  //     aip.dev/not-precedent: lodging_types is clear and follows existing convention --)
  repeated LodgingType lodging_types = 1;     
  // 住宿单元信息列表
  // (-- api-linter: core::0140::lower-snake=disabled
  //     aip.dev/not-precedent: lodging_units is clear and follows existing convention --)
  repeated LodgingUnit lodging_units = 2;
  // 住宿预约信息列表
  // (-- api-linter: core::0140::lower-snake=disabled
  //     aip.dev/not-precedent: lodging_appointments is clear and follows existing convention --)
  repeated LodgingAppointmentInfo lodging_appointments = 3;
  // 宠物信息列表
  repeated PetInfo pets = 4;
  // (-- api-linter: core::0140::lower-snake=disabled
  //     aip.dev/not-precedent: daily_pet_counts is clear and follows existing convention --)
  // 按日期分组的统计数据
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: daily_pet_counts is clear and descriptive --)
  repeated DailyPetCount daily_pet_counts = 5;
  // (-- api-linter: core::0140::lower-snake=disabled
  //     aip.dev/not-precedent: service_instances is clear and follows existing convention --)
  // 服务实例列表
  repeated CalendarServiceInstance service_instances = 6;
}

// CalendarServiceInstance 服务实例
message CalendarServiceInstance {
  // id
  int64 id = 1;
  // is split lodging
  bool is_split_lodging = 2;
  // date type
  DateType date_type = 3;
  // care type
  backend.proto.offering.v1.CareCategory care_type = 4;
  // start date
  google.type.Date start_date = 5;
  // end date
  google.type.Date end_date = 6;
  // specific dates
  repeated google.type.Date specific_dates = 7;
}
