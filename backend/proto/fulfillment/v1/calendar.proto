syntax = "proto3";

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: lodging package structure is appropriate for this domain --)
package backend.proto.fulfillment.v1;

import "google/protobuf/timestamp.proto";
import "buf/validate/validate.proto";
import "backend/proto/offering/v1/service.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

// DailyCapacityEntry 每日容量条目
message DailyCapacityEntry {
    // 日期 (格式: YYYY-MM-DD)
    string date = 1;
    // 使用的容量
    int32 used = 2;
}

// LodgingType 住宿信息
message LodgingType {
    // 住宿ID
    int64 id = 1;
    // 住宿类型
    int32 unit_type = 2;
    // 住宿名称
    string name = 3;
    // 最大宠物数量
    int32 max_pet_number = 4;
    // 最大宠物总数
    int32 max_pet_total_number = 5;
    // 每日容量使用情况 (按日期排序)
    repeated DailyCapacityEntry daily_capacity_used = 6;
    // 是否需要宠物大小过滤
    bool need_pet_size_filter = 7;
    // 宠物大小ID
    repeated int64 pet_size_ids = 8;
    // 总容量
    int32 total_capacity = 9;
}

// LodgingUnit 住宿单元
message LodgingUnit {
    // 住宿单元ID
    int64 id = 1;
    // 住宿单元名称
    string name = 2;
    // 住宿类型ID
    int64 lodging_type_id = 3;
}

// LodgingAppointmentInfo 住宿预约信息
message LodgingAppointmentInfo {
    // 预约ID
    int64 id = 1;
    // 客户ID
    int64 customer_id = 2;
    // 颜色代码
    string color_code = 3;
    // 开始时间
    google.protobuf.Timestamp start_time = 4;
    // 结束时间
    google.protobuf.Timestamp end_time = 5;
    // 服务实例ID
    int64 service_instance_id = 6;
    // 住宿单元ID
    int64 lodging_unit_id = 7;
    // 住宿类型ID
    int64 lodging_type_id = 8;
}

// LodgingFilter 住宿过滤条件
message LodgingFilter {
    // 住宿类型ID列表
    repeated int64 lodging_type_ids = 1 [(buf.validate.field).repeated = {
        max_items: 100
        unique: true
        items: {
          int64: {gte: 0}
        }
    }];
}

// PetInfo 宠物信息
message PetInfo {
    // 宠物ID
    int64 id = 1;
    // 宠物头像路径
    // (-- api-linter: core::0140::prepositions=disabled
    //     aip.dev/not-precedent: avatar_path is clear and descriptive --)
    string avatar_path = 2;
    // 宠物名称
    string name = 3;
    // 宠物类型
    int32 type = 4;
  }
  
  // DailyPetCount 每日宠物数量统计
  message DailyPetCount {
    // 日期（YYYY-MM-DD格式）
    // (-- api-linter: core::0142::time-field-type=disabled
    //     aip.dev/not-precedent: string date format is appropriate for daily statistics --)
    string date = 1;
    // 按care type分组的宠物数量统计
    // key: care_type_id (int64)
    // value: 该care type的宠物数量 (int32)
    // (-- api-linter: core::0140::prepositions=disabled
    //     aip.dev/not-precedent: We need to do this because reasons. --)
    map<int64, int32> pet_care_type_count = 2;
    // 需要住宿的宠物数量
    // (-- api-linter: core::0140::prepositions=disabled
    //     aip.dev/not-precedent: pet_count_with_lodging is clear and descriptive --)
    int32 pet_count_with_lodging = 3;
  }
