// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/fulfillment_service.proto

package fulfillmentpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ListFulfillmentRequest
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: 无效的parent定义 --)
//
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 不需要page-size --)
//
// (-- api-linter: core::0158::request-page_token-field=disabled
//
//	aip.dev/not-precedent: 不需要page_token --)
//
// (-- api-linter: core::0158::request-next_page_token-field=disabled
//
//	aip.dev/not-precedent: 不需要next_page_token --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要page-token --)
type ListFulfillmentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: company_id is clear and descriptive --)
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: business_id is clear and descriptive --)
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 查询开始时间
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: start_time is clear and descriptive --)
	StartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 查询结束时间
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: end_time is clear and descriptive --)
	EndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 过滤条件
	// (-- api-linter: core::0132::request-field-types=disabled
	//
	//	aip.dev/not-precedent: 打平成string不利用协议理解 --)
	Filter *FulfillmentFilter `protobuf:"bytes,5,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序方式
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: sort_type is clear and descriptive --)
	SortType SortType `protobuf:"varint,6,opt,name=sort_type,json=sortType,proto3,enum=backend.proto.fulfillment.v1.SortType" json:"sort_type,omitempty"`
	// 分页信息
	Pagination    *PaginationRef `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentRequest) Reset() {
	*x = ListFulfillmentRequest{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentRequest) ProtoMessage() {}

func (x *ListFulfillmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentRequest.ProtoReflect.Descriptor instead.
func (*ListFulfillmentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListFulfillmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListFulfillmentRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListFulfillmentRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ListFulfillmentRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ListFulfillmentRequest) GetFilter() *FulfillmentFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListFulfillmentRequest) GetSortType() SortType {
	if x != nil {
		return x.SortType
	}
	return SortType_SORT_TYPE_UNSPECIFIED
}

func (x *ListFulfillmentRequest) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// ListFulfillmentResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: 不需要next-page-token --)
type ListFulfillmentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 履约列表
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Fulfillments []*Fulfillment `protobuf:"bytes,1,rep,name=fulfillments,proto3" json:"fulfillments,omitempty"`
	// 分页信息
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Pagination *PaginationRef `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// 是否最后一页
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	//
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: is_end is clear and descriptive --)
	IsEnd bool `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	// 总条数
	// (-- api-linter: core::0132::response-unknown-fields=disabled
	//
	//	aip.dev/not-precedent: 必要的参数 --)
	Total         int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFulfillmentResponse) Reset() {
	*x = ListFulfillmentResponse{}
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFulfillmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFulfillmentResponse) ProtoMessage() {}

func (x *ListFulfillmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFulfillmentResponse.ProtoReflect.Descriptor instead.
func (*ListFulfillmentResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListFulfillmentResponse) GetFulfillments() []*Fulfillment {
	if x != nil {
		return x.Fulfillments
	}
	return nil
}

func (x *ListFulfillmentResponse) GetPagination() *PaginationRef {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListFulfillmentResponse) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

func (x *ListFulfillmentResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_backend_proto_fulfillment_v1_fulfillment_service_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc = "" +
	"\n" +
	"6backend/proto/fulfillment/v1/fulfillment_service.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a.backend/proto/fulfillment/v1/fulfillment.proto\x1a)backend/proto/fulfillment/v1/common.proto\x1a\x1bbuf/validate/validate.proto\"\xb7\x03\n" +
	"\x16ListFulfillmentRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x129\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12G\n" +
	"\x06filter\x18\x05 \x01(\v2/.backend.proto.fulfillment.v1.FulfillmentFilterR\x06filter\x12C\n" +
	"\tsort_type\x18\x06 \x01(\x0e2&.backend.proto.fulfillment.v1.SortTypeR\bsortType\x12K\n" +
	"\n" +
	"pagination\x18\a \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefR\n" +
	"pagination\"\xe2\x01\n" +
	"\x17ListFulfillmentResponse\x12M\n" +
	"\ffulfillments\x18\x01 \x03(\v2).backend.proto.fulfillment.v1.FulfillmentR\ffulfillments\x12K\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2+.backend.proto.fulfillment.v1.PaginationRefR\n" +
	"pagination\x12\x15\n" +
	"\x06is_end\x18\x03 \x01(\bR\x05isEnd\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x05R\x05total2\x94\x01\n" +
	"\x12FulfillmentService\x12~\n" +
	"\x0fListFulfillment\x124.backend.proto.fulfillment.v1.ListFulfillmentRequest\x1a5.backend.proto.fulfillment.v1.ListFulfillmentResponseBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_fulfillment_v1_fulfillment_service_proto_goTypes = []any{
	(*ListFulfillmentRequest)(nil),  // 0: backend.proto.fulfillment.v1.ListFulfillmentRequest
	(*ListFulfillmentResponse)(nil), // 1: backend.proto.fulfillment.v1.ListFulfillmentResponse
	(*timestamppb.Timestamp)(nil),   // 2: google.protobuf.Timestamp
	(*FulfillmentFilter)(nil),       // 3: backend.proto.fulfillment.v1.FulfillmentFilter
	(SortType)(0),                   // 4: backend.proto.fulfillment.v1.SortType
	(*PaginationRef)(nil),           // 5: backend.proto.fulfillment.v1.PaginationRef
	(*Fulfillment)(nil),             // 6: backend.proto.fulfillment.v1.Fulfillment
}
var file_backend_proto_fulfillment_v1_fulfillment_service_proto_depIdxs = []int32{
	2, // 0: backend.proto.fulfillment.v1.ListFulfillmentRequest.start_time:type_name -> google.protobuf.Timestamp
	2, // 1: backend.proto.fulfillment.v1.ListFulfillmentRequest.end_time:type_name -> google.protobuf.Timestamp
	3, // 2: backend.proto.fulfillment.v1.ListFulfillmentRequest.filter:type_name -> backend.proto.fulfillment.v1.FulfillmentFilter
	4, // 3: backend.proto.fulfillment.v1.ListFulfillmentRequest.sort_type:type_name -> backend.proto.fulfillment.v1.SortType
	5, // 4: backend.proto.fulfillment.v1.ListFulfillmentRequest.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	6, // 5: backend.proto.fulfillment.v1.ListFulfillmentResponse.fulfillments:type_name -> backend.proto.fulfillment.v1.Fulfillment
	5, // 6: backend.proto.fulfillment.v1.ListFulfillmentResponse.pagination:type_name -> backend.proto.fulfillment.v1.PaginationRef
	0, // 7: backend.proto.fulfillment.v1.FulfillmentService.ListFulfillment:input_type -> backend.proto.fulfillment.v1.ListFulfillmentRequest
	1, // 8: backend.proto.fulfillment.v1.FulfillmentService.ListFulfillment:output_type -> backend.proto.fulfillment.v1.ListFulfillmentResponse
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_fulfillment_service_proto_init() }
func file_backend_proto_fulfillment_v1_fulfillment_service_proto_init() {
	if File_backend_proto_fulfillment_v1_fulfillment_service_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_fulfillment_proto_init()
	file_backend_proto_fulfillment_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_fulfillment_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_fulfillment_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_fulfillment_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_v1_fulfillment_service_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_fulfillment_service_proto = out.File
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_fulfillment_service_proto_depIdxs = nil
}
