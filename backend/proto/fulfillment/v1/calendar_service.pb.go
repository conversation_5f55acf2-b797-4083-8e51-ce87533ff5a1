// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/fulfillment/v1/calendar_service.proto

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: lodging package structure is appropriate for this domain --)

package fulfillmentpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetLodgingCalendarRequest 获取住宿日历请求
type GetLodgingCalendarRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 商家ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 查询开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 查询结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 过滤条件
	Filter        *LodgingFilter `protobuf:"bytes,5,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLodgingCalendarRequest) Reset() {
	*x = GetLodgingCalendarRequest{}
	mi := &file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLodgingCalendarRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLodgingCalendarRequest) ProtoMessage() {}

func (x *GetLodgingCalendarRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLodgingCalendarRequest.ProtoReflect.Descriptor instead.
func (*GetLodgingCalendarRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetLodgingCalendarRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetLodgingCalendarRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetLodgingCalendarRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetLodgingCalendarRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *GetLodgingCalendarRequest) GetFilter() *LodgingFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// GetLodgingCalendarResponse 获取住宿日历响应
type GetLodgingCalendarResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 住宿信息列表
	// (-- api-linter: core::0140::lower-snake=disabled
	//
	//	aip.dev/not-precedent: lodging_types is clear and follows existing convention --)
	LodgingTypes []*LodgingType `protobuf:"bytes,1,rep,name=lodging_types,json=lodgingTypes,proto3" json:"lodging_types,omitempty"`
	// 住宿单元信息列表
	// (-- api-linter: core::0140::lower-snake=disabled
	//
	//	aip.dev/not-precedent: lodging_units is clear and follows existing convention --)
	LodgingUnits []*LodgingUnit `protobuf:"bytes,2,rep,name=lodging_units,json=lodgingUnits,proto3" json:"lodging_units,omitempty"`
	// 住宿预约信息列表
	// (-- api-linter: core::0140::lower-snake=disabled
	//
	//	aip.dev/not-precedent: lodging_appointments is clear and follows existing convention --)
	LodgingAppointments []*LodgingAppointmentInfo `protobuf:"bytes,3,rep,name=lodging_appointments,json=lodgingAppointments,proto3" json:"lodging_appointments,omitempty"`
	// 宠物信息列表
	Pets []*PetInfo `protobuf:"bytes,4,rep,name=pets,proto3" json:"pets,omitempty"`
	// (-- api-linter: core::0140::lower-snake=disabled
	//
	//	aip.dev/not-precedent: daily_pet_counts is clear and follows existing convention --)
	//
	// 按日期分组的统计数据
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: daily_pet_counts is clear and descriptive --)
	DailyPetCounts []*DailyPetCount `protobuf:"bytes,5,rep,name=daily_pet_counts,json=dailyPetCounts,proto3" json:"daily_pet_counts,omitempty"`
	// (-- api-linter: core::0140::lower-snake=disabled
	//
	//	aip.dev/not-precedent: service_instances is clear and follows existing convention --)
	//
	// 服务实例列表
	ServiceInstances []*CalendarServiceInstance `protobuf:"bytes,6,rep,name=service_instances,json=serviceInstances,proto3" json:"service_instances,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetLodgingCalendarResponse) Reset() {
	*x = GetLodgingCalendarResponse{}
	mi := &file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLodgingCalendarResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLodgingCalendarResponse) ProtoMessage() {}

func (x *GetLodgingCalendarResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLodgingCalendarResponse.ProtoReflect.Descriptor instead.
func (*GetLodgingCalendarResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetLodgingCalendarResponse) GetLodgingTypes() []*LodgingType {
	if x != nil {
		return x.LodgingTypes
	}
	return nil
}

func (x *GetLodgingCalendarResponse) GetLodgingUnits() []*LodgingUnit {
	if x != nil {
		return x.LodgingUnits
	}
	return nil
}

func (x *GetLodgingCalendarResponse) GetLodgingAppointments() []*LodgingAppointmentInfo {
	if x != nil {
		return x.LodgingAppointments
	}
	return nil
}

func (x *GetLodgingCalendarResponse) GetPets() []*PetInfo {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *GetLodgingCalendarResponse) GetDailyPetCounts() []*DailyPetCount {
	if x != nil {
		return x.DailyPetCounts
	}
	return nil
}

func (x *GetLodgingCalendarResponse) GetServiceInstances() []*CalendarServiceInstance {
	if x != nil {
		return x.ServiceInstances
	}
	return nil
}

// CalendarServiceInstance 服务实例
type CalendarServiceInstance struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// is split lodging
	IsSplitLodging bool `protobuf:"varint,2,opt,name=is_split_lodging,json=isSplitLodging,proto3" json:"is_split_lodging,omitempty"`
	// date type
	DateType DateType `protobuf:"varint,3,opt,name=date_type,json=dateType,proto3,enum=backend.proto.fulfillment.v1.DateType" json:"date_type,omitempty"`
	// care type
	CareType v1.CareCategory `protobuf:"varint,4,opt,name=care_type,json=careType,proto3,enum=backend.proto.offering.v1.CareCategory" json:"care_type,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// specific dates
	SpecificDates []*date.Date `protobuf:"bytes,7,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CalendarServiceInstance) Reset() {
	*x = CalendarServiceInstance{}
	mi := &file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CalendarServiceInstance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarServiceInstance) ProtoMessage() {}

func (x *CalendarServiceInstance) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarServiceInstance.ProtoReflect.Descriptor instead.
func (*CalendarServiceInstance) Descriptor() ([]byte, []int) {
	return file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescGZIP(), []int{2}
}

func (x *CalendarServiceInstance) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CalendarServiceInstance) GetIsSplitLodging() bool {
	if x != nil {
		return x.IsSplitLodging
	}
	return false
}

func (x *CalendarServiceInstance) GetDateType() DateType {
	if x != nil {
		return x.DateType
	}
	return DateType_DATE_TYPE_UNSPECIFIED
}

func (x *CalendarServiceInstance) GetCareType() v1.CareCategory {
	if x != nil {
		return x.CareType
	}
	return v1.CareCategory(0)
}

func (x *CalendarServiceInstance) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *CalendarServiceInstance) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *CalendarServiceInstance) GetSpecificDates() []*date.Date {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

var File_backend_proto_fulfillment_v1_calendar_service_proto protoreflect.FileDescriptor

const file_backend_proto_fulfillment_v1_calendar_service_proto_rawDesc = "" +
	"\n" +
	"3backend/proto/fulfillment/v1/calendar_service.proto\x12\x1cbackend.proto.fulfillment.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x16google/type/date.proto\x1a)backend/proto/fulfillment/v1/common.proto\x1a\x1bbuf/validate/validate.proto\x1a+backend/proto/fulfillment/v1/calendar.proto\x1a)backend/proto/offering/v1/care_type.proto\"\xa4\x02\n" +
	"\x19GetLodgingCalendarRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12(\n" +
	"\vbusiness_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"businessId\x129\n" +
	"\n" +
	"start_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12C\n" +
	"\x06filter\x18\x05 \x01(\v2+.backend.proto.fulfillment.v1.LodgingFilterR\x06filter\"\x9b\x04\n" +
	"\x1aGetLodgingCalendarResponse\x12N\n" +
	"\rlodging_types\x18\x01 \x03(\v2).backend.proto.fulfillment.v1.LodgingTypeR\flodgingTypes\x12N\n" +
	"\rlodging_units\x18\x02 \x03(\v2).backend.proto.fulfillment.v1.LodgingUnitR\flodgingUnits\x12g\n" +
	"\x14lodging_appointments\x18\x03 \x03(\v24.backend.proto.fulfillment.v1.LodgingAppointmentInfoR\x13lodgingAppointments\x129\n" +
	"\x04pets\x18\x04 \x03(\v2%.backend.proto.fulfillment.v1.PetInfoR\x04pets\x12U\n" +
	"\x10daily_pet_counts\x18\x05 \x03(\v2+.backend.proto.fulfillment.v1.DailyPetCountR\x0edailyPetCounts\x12b\n" +
	"\x11service_instances\x18\x06 \x03(\v25.backend.proto.fulfillment.v1.CalendarServiceInstanceR\x10serviceInstances\"\xf8\x02\n" +
	"\x17CalendarServiceInstance\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12(\n" +
	"\x10is_split_lodging\x18\x02 \x01(\bR\x0eisSplitLodging\x12C\n" +
	"\tdate_type\x18\x03 \x01(\x0e2&.backend.proto.fulfillment.v1.DateTypeR\bdateType\x12D\n" +
	"\tcare_type\x18\x04 \x01(\x0e2'.backend.proto.offering.v1.CareCategoryR\bcareType\x120\n" +
	"\n" +
	"start_date\x18\x05 \x01(\v2\x11.google.type.DateR\tstartDate\x12,\n" +
	"\bend_date\x18\x06 \x01(\v2\x11.google.type.DateR\aendDate\x128\n" +
	"\x0especific_dates\x18\a \x03(\v2\x11.google.type.DateR\rspecificDates2\x9b\x01\n" +
	"\x0fCalendarService\x12\x87\x01\n" +
	"\x12GetLodgingCalendar\x127.backend.proto.fulfillment.v1.GetLodgingCalendarRequest\x1a8.backend.proto.fulfillment.v1.GetLodgingCalendarResponseBt\n" +
	"&com.moego.backend.proto.fulfillment.v1P\x01ZHgithub.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpbb\x06proto3"

var (
	file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescOnce sync.Once
	file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescData []byte
)

func file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescGZIP() []byte {
	file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_calendar_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_calendar_service_proto_rawDesc)))
	})
	return file_backend_proto_fulfillment_v1_calendar_service_proto_rawDescData
}

var file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_backend_proto_fulfillment_v1_calendar_service_proto_goTypes = []any{
	(*GetLodgingCalendarRequest)(nil),  // 0: backend.proto.fulfillment.v1.GetLodgingCalendarRequest
	(*GetLodgingCalendarResponse)(nil), // 1: backend.proto.fulfillment.v1.GetLodgingCalendarResponse
	(*CalendarServiceInstance)(nil),    // 2: backend.proto.fulfillment.v1.CalendarServiceInstance
	(*timestamppb.Timestamp)(nil),      // 3: google.protobuf.Timestamp
	(*LodgingFilter)(nil),              // 4: backend.proto.fulfillment.v1.LodgingFilter
	(*LodgingType)(nil),                // 5: backend.proto.fulfillment.v1.LodgingType
	(*LodgingUnit)(nil),                // 6: backend.proto.fulfillment.v1.LodgingUnit
	(*LodgingAppointmentInfo)(nil),     // 7: backend.proto.fulfillment.v1.LodgingAppointmentInfo
	(*PetInfo)(nil),                    // 8: backend.proto.fulfillment.v1.PetInfo
	(*DailyPetCount)(nil),              // 9: backend.proto.fulfillment.v1.DailyPetCount
	(DateType)(0),                      // 10: backend.proto.fulfillment.v1.DateType
	(v1.CareCategory)(0),               // 11: backend.proto.offering.v1.CareCategory
	(*date.Date)(nil),                  // 12: google.type.Date
}
var file_backend_proto_fulfillment_v1_calendar_service_proto_depIdxs = []int32{
	3,  // 0: backend.proto.fulfillment.v1.GetLodgingCalendarRequest.start_time:type_name -> google.protobuf.Timestamp
	3,  // 1: backend.proto.fulfillment.v1.GetLodgingCalendarRequest.end_time:type_name -> google.protobuf.Timestamp
	4,  // 2: backend.proto.fulfillment.v1.GetLodgingCalendarRequest.filter:type_name -> backend.proto.fulfillment.v1.LodgingFilter
	5,  // 3: backend.proto.fulfillment.v1.GetLodgingCalendarResponse.lodging_types:type_name -> backend.proto.fulfillment.v1.LodgingType
	6,  // 4: backend.proto.fulfillment.v1.GetLodgingCalendarResponse.lodging_units:type_name -> backend.proto.fulfillment.v1.LodgingUnit
	7,  // 5: backend.proto.fulfillment.v1.GetLodgingCalendarResponse.lodging_appointments:type_name -> backend.proto.fulfillment.v1.LodgingAppointmentInfo
	8,  // 6: backend.proto.fulfillment.v1.GetLodgingCalendarResponse.pets:type_name -> backend.proto.fulfillment.v1.PetInfo
	9,  // 7: backend.proto.fulfillment.v1.GetLodgingCalendarResponse.daily_pet_counts:type_name -> backend.proto.fulfillment.v1.DailyPetCount
	2,  // 8: backend.proto.fulfillment.v1.GetLodgingCalendarResponse.service_instances:type_name -> backend.proto.fulfillment.v1.CalendarServiceInstance
	10, // 9: backend.proto.fulfillment.v1.CalendarServiceInstance.date_type:type_name -> backend.proto.fulfillment.v1.DateType
	11, // 10: backend.proto.fulfillment.v1.CalendarServiceInstance.care_type:type_name -> backend.proto.offering.v1.CareCategory
	12, // 11: backend.proto.fulfillment.v1.CalendarServiceInstance.start_date:type_name -> google.type.Date
	12, // 12: backend.proto.fulfillment.v1.CalendarServiceInstance.end_date:type_name -> google.type.Date
	12, // 13: backend.proto.fulfillment.v1.CalendarServiceInstance.specific_dates:type_name -> google.type.Date
	0,  // 14: backend.proto.fulfillment.v1.CalendarService.GetLodgingCalendar:input_type -> backend.proto.fulfillment.v1.GetLodgingCalendarRequest
	1,  // 15: backend.proto.fulfillment.v1.CalendarService.GetLodgingCalendar:output_type -> backend.proto.fulfillment.v1.GetLodgingCalendarResponse
	15, // [15:16] is the sub-list for method output_type
	14, // [14:15] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_backend_proto_fulfillment_v1_calendar_service_proto_init() }
func file_backend_proto_fulfillment_v1_calendar_service_proto_init() {
	if File_backend_proto_fulfillment_v1_calendar_service_proto != nil {
		return
	}
	file_backend_proto_fulfillment_v1_common_proto_init()
	file_backend_proto_fulfillment_v1_calendar_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_fulfillment_v1_calendar_service_proto_rawDesc), len(file_backend_proto_fulfillment_v1_calendar_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_fulfillment_v1_calendar_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_fulfillment_v1_calendar_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_fulfillment_v1_calendar_service_proto_msgTypes,
	}.Build()
	File_backend_proto_fulfillment_v1_calendar_service_proto = out.File
	file_backend_proto_fulfillment_v1_calendar_service_proto_goTypes = nil
	file_backend_proto_fulfillment_v1_calendar_service_proto_depIdxs = nil
}
