syntax = "proto3";

package backend.proto.fulfillment.v1;
import "google/protobuf/timestamp.proto";
import "backend/proto/fulfillment/v1/fulfillment.proto";
import "backend/proto/fulfillment/v1/common.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1;fulfillmentpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.fulfillment.v1";

import "buf/validate/validate.proto";

// FulfillmentService
service FulfillmentService {
  // ListFulfillment 获取履约信息
  rpc ListFulfillment(ListFulfillmentRequest) returns (ListFulfillmentResponse);
}

// ListFulfillmentRequest
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 无效的parent定义 --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 不需要page-size --)
// (-- api-linter: core::0158::request-page_token-field=disabled
//     aip.dev/not-precedent: 不需要page_token --)
// (-- api-linter: core::0158::request-next_page_token-field=disabled
//     aip.dev/not-precedent: 不需要next_page_token --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 不需要page-token --)
message ListFulfillmentRequest {
  // 公司ID
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: company_id is clear and descriptive --)
  int64 company_id = 1 [(buf.validate.field).int64.gt = 0];
  // 商家ID
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: business_id is clear and descriptive --)
  int64 business_id = 2 [(buf.validate.field).int64.gt = 0];
  // 查询开始时间
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: start_time is clear and descriptive --)
  google.protobuf.Timestamp start_time = 3;
  // 查询结束时间
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: end_time is clear and descriptive --)
  google.protobuf.Timestamp end_time = 4;
  // 过滤条件
  // (-- api-linter: core::0132::request-field-types=disabled
  //     aip.dev/not-precedent: 打平成string不利用协议理解 --)
  FulfillmentFilter filter = 5;
  // 排序方式
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: sort_type is clear and descriptive --)
  SortType sort_type = 6;
  // 分页信息
  PaginationRef pagination = 7;
}

// ListFulfillmentResponse
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 不需要next-page-token --)
message ListFulfillmentResponse {
  // 履约列表
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  repeated Fulfillment fulfillments = 1;
  // 分页信息
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  PaginationRef pagination = 2;
  // 是否最后一页
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: is_end is clear and descriptive --)
  bool is_end = 3;
  // 总条数
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: 必要的参数 --)
  int32 total = 4;
}