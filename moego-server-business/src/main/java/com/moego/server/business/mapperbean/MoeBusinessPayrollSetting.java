package com.moego.server.business.mapperbean;

import com.moego.server.business.dto.BusinessPayrollSettingDTO.TipAllocationType;
import java.util.Date;

public class MoeBusinessPayrollSetting {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.split_tips_method
     *
     * @mbg.generated
     */
    private Byte splitTipsMethod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.new_payroll_enable
     *
     * @mbg.generated
     */
    private Boolean newPayrollEnable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.service_commission_based
     *
     * @mbg.generated
     */
    private Byte serviceCommissionBased;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_payroll_setting.tip_allocation_type
     *
     * @mbg.generated
     */
    private TipAllocationType tipAllocationType;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payroll_setting.id
     *
     * @return the value of moe_business_payroll_setting.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payroll_setting.id
     *
     * @param id the value for moe_business_payroll_setting.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payroll_setting.business_id
     *
     * @return the value of moe_business_payroll_setting.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payroll_setting.business_id
     *
     * @param businessId the value for moe_business_payroll_setting.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payroll_setting.split_tips_method
     *
     * @return the value of moe_business_payroll_setting.split_tips_method
     *
     * @mbg.generated
     */
    public Byte getSplitTipsMethod() {
        return splitTipsMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payroll_setting.split_tips_method
     *
     * @param splitTipsMethod the value for moe_business_payroll_setting.split_tips_method
     *
     * @mbg.generated
     */
    public void setSplitTipsMethod(Byte splitTipsMethod) {
        this.splitTipsMethod = splitTipsMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payroll_setting.new_payroll_enable
     *
     * @return the value of moe_business_payroll_setting.new_payroll_enable
     *
     * @mbg.generated
     */
    public Boolean getNewPayrollEnable() {
        return newPayrollEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payroll_setting.new_payroll_enable
     *
     * @param newPayrollEnable the value for moe_business_payroll_setting.new_payroll_enable
     *
     * @mbg.generated
     */
    public void setNewPayrollEnable(Boolean newPayrollEnable) {
        this.newPayrollEnable = newPayrollEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payroll_setting.service_commission_based
     *
     * @return the value of moe_business_payroll_setting.service_commission_based
     *
     * @mbg.generated
     */
    public Byte getServiceCommissionBased() {
        return serviceCommissionBased;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payroll_setting.service_commission_based
     *
     * @param serviceCommissionBased the value for moe_business_payroll_setting.service_commission_based
     *
     * @mbg.generated
     */
    public void setServiceCommissionBased(Byte serviceCommissionBased) {
        this.serviceCommissionBased = serviceCommissionBased;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payroll_setting.create_time
     *
     * @return the value of moe_business_payroll_setting.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payroll_setting.create_time
     *
     * @param createTime the value for moe_business_payroll_setting.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payroll_setting.update_time
     *
     * @return the value of moe_business_payroll_setting.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payroll_setting.update_time
     *
     * @param updateTime the value for moe_business_payroll_setting.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payroll_setting.company_id
     *
     * @return the value of moe_business_payroll_setting.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payroll_setting.company_id
     *
     * @param companyId the value for moe_business_payroll_setting.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_payroll_setting.tip_allocation_type
     *
     * @return the value of moe_business_payroll_setting.tip_allocation_type
     *
     * @mbg.generated
     */
    public TipAllocationType getTipAllocationType() {
        return tipAllocationType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_payroll_setting.tip_allocation_type
     *
     * @param tipAllocationType the value for moe_business_payroll_setting.tip_allocation_type
     *
     * @mbg.generated
     */
    public void setTipAllocationType(TipAllocationType tipAllocationType) {
        this.tipAllocationType = tipAllocationType;
    }
}
