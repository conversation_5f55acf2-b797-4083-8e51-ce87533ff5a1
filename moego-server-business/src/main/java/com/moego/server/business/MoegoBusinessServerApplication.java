package com.moego.server.business;

import com.moego.lib.aws.S3API;
import com.moego.server.business.config.SessionConfig;
import java.util.TimeZone;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import software.amazon.awssdk.regions.Region;

@Slf4j
@EnableFeignClients({
    "com.moego.server.customer.client",
    "com.moego.server.retail.client",
    "com.moego.server.message.client",
    "com.moego.server.grooming.client",
    "com.moego.server.payment.client",
    "com.moego.api.thirdparty",
})
@SpringBootApplication(proxyBeanMethods = false)
@ConfigurationPropertiesScan
@MapperScan("com.moego.server.business.mapper")
public class MoegoBusinessServerApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        var app = SpringApplication.run(MoegoBusinessServerApplication.class, args);

        var sessionConfig = app.getBean(SessionConfig.class);
        log.info("session config: {}", sessionConfig);
    }

    @Bean
    public S3API s3API(
            @Value("${s3.key}") String accessKey,
            @Value("${s3.secret}") String accessSecret,
            @Value("${s3.region}") String clientRegion) {
        return new S3API(Region.of(clientRegion), accessKey, accessSecret);
    }
}
