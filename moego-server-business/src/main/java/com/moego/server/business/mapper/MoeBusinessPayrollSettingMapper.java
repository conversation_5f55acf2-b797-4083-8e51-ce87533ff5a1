package com.moego.server.business.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.server.business.mapperbean.MoeBusinessPayrollSetting;
import com.moego.server.business.mapperbean.MoeBusinessPayrollSettingExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBusinessPayrollSettingMapper extends DynamicDataSource<MoeBusinessPayrollSettingMapper> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    long countByExample(MoeBusinessPayrollSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    int insert(MoeBusinessPayrollSetting row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessPayrollSetting row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    List<MoeBusinessPayrollSetting> selectByExample(MoeBusinessPayrollSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    MoeBusinessPayrollSetting selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeBusinessPayrollSetting row, @Param("example") MoeBusinessPayrollSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("row") MoeBusinessPayrollSetting row, @Param("example") MoeBusinessPayrollSettingExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessPayrollSetting row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payroll_setting
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessPayrollSetting row);
}
