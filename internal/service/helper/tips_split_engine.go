package helper

import (
	"sort"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/common/proto/money"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/core"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/business"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/grooming"
)

const (
	// 百分比
	percentage100 = 100
	// 百分数
	percentageRound2 = 2
	// 普通小数
	decimalRound4 = 4
)

type TipsSplitEngine interface {
	CalculateTipsSplit(
		apptDetailList []*grooming.PetDetailDTO,
		apptSetting *orderpb.TipsSplitModel_TipsSplitConfig,
		totalTipsAmount decimal.Decimal,
		currencyCode string,
		productAssignedStaffs []int64,
		tipAllocationType string,
	) (*orderpb.TipsSplitModel_TipsSplitConfig, error)
}

type tipsSplitEngineImpl struct{}

// CalculateTipsSplit implements TipsSplitEngine.
func (t *tipsSplitEngineImpl) CalculateTipsSplit(
	apptDetailList []*grooming.PetDetailDTO,
	apptSetting *orderpb.TipsSplitModel_TipsSplitConfig,
	totalTipAmount decimal.Decimal,
	curreyCode string,
	productAssignedStaffs []int64,
	tipAllocationType string,
) (*orderpb.TipsSplitModel_TipsSplitConfig, error) {
	// 1. 计算出每个员工的小费金额和小费比例
	// 2. 返回 TipsSplitConfig
	// 3. 如果是平分，productAssignedStaffs参与分配，其他场景无需修改
	if len(apptDetailList) == 0 && len(productAssignedStaffs) == 0 {
		return nil, status.Error(codes.InvalidArgument, "not found any staff can get tips")
	}

	if totalTipAmount.IsNegative() {
		return nil, status.Error(codes.InvalidArgument, "total tips amount must not be negative")
	}

	// Calculate business and staff tip amounts based on tipAllocationType
	var businessTipAmount, staffTipAmount decimal.Decimal

	switch tipAllocationType {
	case business.TipAllocationTypeAllToStaff:
		// All tips go to staff
		businessTipAmount = decimal.Zero
		staffTipAmount = totalTipAmount
	case business.TipAllocationTypeByService:
		// Use original logic: split based on service assignment
		businessTipAmount = calculateBusinessTipAmountByService(totalTipAmount, apptDetailList)
		staffTipAmount = totalTipAmount.Sub(businessTipAmount)
	default:
		// Default to ALL_TO_STAFF for backward compatibility
		businessTipAmount = decimal.Zero
		staffTipAmount = totalTipAmount
	}

	if businessTipAmount.IsNegative() {
		return nil, status.Error(codes.InvalidArgument, "business tip amount must not be negative")
	}

	if staffTipAmount.IsNegative() {
		return nil, status.Error(codes.InvalidArgument, "staff tip amount must not be negative")
	}

	staffIDSet := getStaffIDSetByPetDetailList(apptDetailList)
	// Build the result config
	result := &orderpb.TipsSplitModel_TipsSplitConfig{
		SplitMethod:       apptSetting.SplitMethod,
		StaffConfigs:      make([]*orderpb.StaffTipConfig, 0, len(staffIDSet)),
		BusinessTipAmount: money.FromDecimal(businessTipAmount, curreyCode),
	}

	switch apptSetting.SplitMethod {
	case orderpb.SplitTipsMethod_SPLIT_TIPS_METHOD_UNSPECIFIED:
		return nil, status.Error(codes.InvalidArgument, "split method is unspecified")

	case orderpb.SplitTipsMethod_SPLIT_TIPS_METHOD_BY_SERVICE:
		splitRateAndAmountMap := BuildSplitRateAndAmountByService(staffTipAmount, apptDetailList)
		for staffID, pair := range splitRateAndAmountMap {
			result.StaffConfigs = append(
				result.StaffConfigs, &orderpb.StaffTipConfig{
					StaffId:    staffID,
					Percentage: pair.SplitRate.Mul(decimal.NewFromInt(percentage100)).Round(percentageRound2).InexactFloat64(),
					Amount:     money.FromDecimal(pair.Amount, curreyCode),
				},
			)
		}

	case orderpb.SplitTipsMethod_SPLIT_TIPS_METHOD_BY_EQUALLY:
		// 平分时，加上 product 的 staff
		staffIDSet = append(staffIDSet, productAssignedStaffs...)
		staffIDSet = lo.Uniq(lo.Filter(staffIDSet, func(it int64, _ int) bool { return it > 0 }))

		for staffID, pair := range BuildStaffSplitRateAndAmountEqually(staffIDSet, staffTipAmount) {
			result.StaffConfigs = append(
				result.StaffConfigs, &orderpb.StaffTipConfig{
					StaffId:    staffID,
					Percentage: pair.SplitRate.Mul(decimal.NewFromInt(percentage100)).Round(percentageRound2).InexactFloat64(),
					Amount:     money.FromDecimal(pair.Amount, curreyCode),
				},
			)
		}

	case orderpb.SplitTipsMethod_SPLIT_TIPS_METHOD_BY_PERCENTAGE:
		sum := decimal.Zero
		for _, config := range apptSetting.StaffConfigs {
			sum = sum.Add(decimal.NewFromFloat(config.Percentage))
			// compute each staff's amount
			splitPercent := decimal.NewFromFloat(config.Percentage).Div(decimal.NewFromInt(percentage100))
			config.Amount = money.FromDecimal(staffTipAmount.Mul(splitPercent), curreyCode)
		}

		if sum.Cmp(decimal.NewFromInt(percentage100)) != 0 {
			return nil, status.Error(codes.InvalidArgument, "sum of percentage is not equal to 100")
		}

		result.StaffConfigs = apptSetting.StaffConfigs

	case orderpb.SplitTipsMethod_SPLIT_TIPS_METHOD_BY_FIXED_AMOUNT:
		sum := decimal.Zero
		for _, config := range apptSetting.StaffConfigs {
			sum = sum.Add(money.ToDecimal(config.Amount))
		}

		if !sum.Equal(staffTipAmount) {
			return nil, status.Error(codes.InvalidArgument, "sum of staff config is not equal to total tips amount")
		}

		result.StaffConfigs = apptSetting.StaffConfigs

	default:
		return nil, status.Error(codes.InvalidArgument, "split method is invalid")
	}

	return result, nil
}

type Pair struct {
	SplitRate decimal.Decimal
	Amount    decimal.Decimal
}

func BuildStaffSplitRateAndAmountEqually(staffIDSet []int64, totalTipsAmount decimal.Decimal) map[int64]Pair {
	if len(staffIDSet) == 0 {
		return nil
	}

	resultMap := make(map[int64]Pair)
	// 平均分配保留四位小数
	averageRate := decimal.NewFromInt(1).Div(decimal.NewFromInt(int64(len(staffIDSet)))).Round(decimalRound4)
	totalAllocatedTips := decimal.NewFromInt(0)

	for _, staffID := range staffIDSet {
		allocatedAmount := totalTipsAmount.Mul(averageRate).Round(percentageRound2)
		totalAllocatedTips = totalAllocatedTips.Add(allocatedAmount)
		resultMap[staffID] = Pair{
			SplitRate: averageRate,
			Amount:    allocatedAmount,
		}
	}
	// 处理轧差
	difference := totalTipsAmount.Sub(totalAllocatedTips)
	if difference.GreaterThan(decimal.Zero) {
		// 将误差加到第一个 staffID 的金额中
		firstStaffID := staffIDSet[0]
		originalPair := resultMap[firstStaffID]
		adjustedAmount := originalPair.Amount.Add(difference)
		resultMap[firstStaffID] = Pair{SplitRate: originalPair.SplitRate, Amount: adjustedAmount}
	}

	return resultMap
}

func BuildSplitRateAndAmountByService(
	amount decimal.Decimal,
	groomingPetDetailDTOList []*grooming.PetDetailDTO,
) map[int64]Pair {
	// 只在有 Staff 的 Service 之间分 Tips.
	// 为了方便，这里按从小到大的顺序排一下.
	petDetailWithStaff := lo.Filter(
		groomingPetDetailDTOList,
		func(it *grooming.PetDetailDTO, _ int) bool {
			return IsPetDetailAssignedStaff(it)
		},
	)

	sort.Slice(
		petDetailWithStaff, func(i, j int) bool {
			pdi := petDetailWithStaff[i]
			pdj := petDetailWithStaff[j]

			if pdi.GetServicePrice().Equal(pdj.GetServicePrice()) {
				return pdi.ID < pdj.ID
			}

			return pdi.GetServicePrice().LessThan(pdj.GetServicePrice())
		},
	)

	// 先把 PetDetail 级别的 Tips Amount & Rate 分好.
	totalServiceAmount := decimal.Zero
	for _, dto := range petDetailWithStaff {
		totalServiceAmount = totalServiceAmount.Add(dto.GetServicePrice())
	}

	pdIDToPair := make(map[int64]Pair, len(petDetailWithStaff))
	rateBalance := decimal.NewFromInt32(1)
	tipBalance := amount

	for idx, pd := range petDetailWithStaff {
		rate := decimal.Zero
		if totalServiceAmount.IsPositive() {
			rate = core.RoundRate(
				pd.GetServicePrice().Div(totalServiceAmount),
			)
		}

		if rate.GreaterThan(rateBalance) {
			rate = rateBalance
		}

		tip := core.RoundPreTaxAmount(amount.Mul(rate))
		if tip.GreaterThan(tipBalance) {
			tip = tipBalance
		}

		// 处理轧差.
		if idx == len(petDetailWithStaff)-1 {
			rate = rateBalance
			tip = tipBalance
		}

		pdIDToPair[pd.ID] = Pair{
			SplitRate: rate,
			Amount:    tip,
		}

		rateBalance = rateBalance.Sub(rate)
		tipBalance = tipBalance.Sub(tip)
	}

	// 再处理每一个 PetDetail 内的 Tips 分配，并聚合到 Staff 级别.
	staffIDToPair := make(map[int64]Pair, len(petDetailWithStaff))

	for _, pd := range petDetailWithStaff {
		res := splitPairBetweenStaffs(pdIDToPair[pd.ID], SplitTipRateWithinOneService(pd))

		for staffID, pair := range res {
			// Staff 可能出现在多个 Service 内，这里需要聚合同一个 Staff 的结果.
			exists, ok := staffIDToPair[staffID]
			if !ok {
				exists = Pair{
					SplitRate: decimal.Zero,
					Amount:    decimal.Zero,
				}
			}

			exists.SplitRate = exists.SplitRate.Add(pair.SplitRate)
			exists.Amount = exists.Amount.Add(pair.Amount)
			staffIDToPair[staffID] = exists
		}
	}

	return staffIDToPair
}

func splitPairBetweenStaffs(pair Pair, staffIDToRate map[int64]decimal.Decimal) map[int64]Pair {
	// 按照比例从小到大排序，相等的话，staff ID 小的在前.
	staffIDs := lo.Keys(staffIDToRate)
	sort.Slice(
		staffIDs, func(i, j int) bool {
			idi := staffIDs[i]
			idj := staffIDs[j]
			ri := staffIDToRate[idi]
			rj := staffIDToRate[idj]

			if ri.Equal(rj) {
				return idi < idj
			}

			return ri.LessThan(rj)
		},
	)

	staffToPair := make(map[int64]Pair, len(staffIDs))
	rateBalance := pair.SplitRate
	tipBalance := pair.Amount

	for idx, staffID := range staffIDs {
		rate := staffIDToRate[staffID]

		// 按照 Service 内占比分摊 Pair 的比例.
		pairRate := core.RoundRate(pair.SplitRate.Mul(rate))
		if pairRate.GreaterThan(rateBalance) {
			rate = rateBalance
		}

		pairTip := core.RoundPreTaxAmount(pair.Amount.Mul(rate))
		if pairTip.GreaterThan(tipBalance) {
			pairTip = tipBalance
		}

		// 处理轧差.
		if idx == len(staffIDs)-1 {
			pairRate = rateBalance
			pairTip = tipBalance
		}

		rateBalance = rateBalance.Sub(pairRate)
		tipBalance = tipBalance.Sub(pairTip)

		staffToPair[staffID] = Pair{
			SplitRate: pairRate,
			Amount:    pairTip,
		}
	}

	return staffToPair
}

// SplitTipRateWithinOneService 处理单个 Service 内有多个 Staff 的场景.
func SplitTipRateWithinOneService(groomingPetDetailDTO *grooming.PetDetailDTO) map[int64]decimal.Decimal {
	staffIDSplitRateMap := make(map[int64]decimal.Decimal)
	if len(groomingPetDetailDTO.OperationList) == 0 {
		staffIDSplitRateMap[groomingPetDetailDTO.StaffID] = decimal.NewFromInt(1)
	} else {
		for _, operationDTO := range groomingPetDetailDTO.OperationList {
			staffIDSplitRateMap[operationDTO.StaffID] = operationDTO.PriceRatio
		}
	}

	return staffIDSplitRateMap
}

func IsPetDetailAssignedStaff(petDetail *grooming.PetDetailDTO) bool {
	if len(petDetail.OperationList) == 0 {
		return petDetail.StaffID > 0
	}

	for _, operation := range petDetail.OperationList {
		if operation.StaffID > 0 {
			return true
		}
	}

	return false
}

func getStaffIDSetByPetDetailList(groomingPetDetailDTOList []*grooming.PetDetailDTO) []int64 {
	staffIDs := make(map[int64]struct{})

	for _, groomingPetDetailDTO := range groomingPetDetailDTOList {
		if len(groomingPetDetailDTO.OperationList) == 0 {
			staffIDs[groomingPetDetailDTO.StaffID] = struct{}{}
		} else {
			for _, operation := range groomingPetDetailDTO.OperationList {
				staffIDs[operation.StaffID] = struct{}{}
			}
		}
	}

	// Ignore unassigned staff.
	delete(staffIDs, 0)

	return lo.Keys(staffIDs)
}

// calculateBusinessTipAmountByService calculates business tip amount based on service assignment
// Services without staff get tips allocated to business, services with staff get tips allocated to staff
func calculateBusinessTipAmountByService(totalTipAmount decimal.Decimal, apptDetailList []*grooming.PetDetailDTO) decimal.Decimal {
	if len(apptDetailList) == 0 {
		return decimal.Zero
	}

	// Calculate total service amount and service amount without staff
	totalServiceAmount := decimal.Zero
	serviceWithoutStaffAmount := decimal.Zero

	for _, petDetail := range apptDetailList {
		servicePrice := petDetail.GetServicePrice()
		totalServiceAmount = totalServiceAmount.Add(servicePrice)

		// Check if this service has no staff assigned
		if !IsPetDetailAssignedStaff(petDetail) {
			serviceWithoutStaffAmount = serviceWithoutStaffAmount.Add(servicePrice)
		}
	}

	// If total service amount is zero, return zero business tip
	if totalServiceAmount.IsZero() {
		return decimal.Zero
	}

	// Calculate business tip amount proportionally
	businessTipRatio := serviceWithoutStaffAmount.Div(totalServiceAmount)
	return totalTipAmount.Mul(businessTipRatio).Round(decimalRound4)
}

func NewTipsSplitEngine() TipsSplitEngine {
	return &tipsSplitEngineImpl{}
}
