package business

import (
	"bytes"
	"context"
	"encoding/json"
	"net/url"
	"strconv"

	mhttp "github.com/MoeGolibrary/go-lib/invoke/http/client"

	httputil "github.com/MoeGolibrary/moego-svc-payment/internal/utils/http"
)

//go:generate mockgen -destination=./mock/business_client_mock.go -package=mock github.com/MoeGolibrary/moego-svc-payment/internal/repo/business Client
type Client interface {
	GetBusiness(ctx context.Context, businessID int64) (*DTO, error)
	MapBusiness(ctx context.Context, businessIDs []int64) (map[int64]*DTO, error)
	GetCompany(ctx context.Context, businessID int64) (*CompanyDTO, error)
}

type clientImpl struct {
	cli mhttp.Client
}

func NewClient() Client {
	return &clientImpl{
		cli: mhttp.NewClient("http://moego-service-business:9203"),
	}
}

func (c *clientImpl) GetBusiness(ctx context.Context, businessID int64) (*DTO, error) {
	const path = "/service/business/business/getBusinessInfo"
	params := map[string]any{
		"infoId": businessID,
	}
	payload, err := json.Marshal(
		params,
	)
	if err != nil {
		return nil, err
	}
	resp, err := c.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	defer func() { _ = resp.Body.Close() }()

	if err != nil {
		return nil, err
	}

	result, err := httputil.ParseResponse[DTO](resp)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *clientImpl) MapBusiness(ctx context.Context, businessIDs []int64) (map[int64]*DTO, error) {
	const path = "/service/business/business/getOnlyBusinessInfoBatch"

	if len(businessIDs) == 0 {
		return nil, nil
	}

	payload, err := json.Marshal(
		businessIDs,
	)
	if err != nil {
		return nil, err
	}

	resp, err := c.cli.Post(ctx, path, nil, bytes.NewReader(payload))
	defer func() { _ = resp.Body.Close() }()

	if err != nil {
		return nil, err
	}

	result, err := httputil.ParseResponse[map[int64]*DTO](resp)
	if err != nil {
		return nil, err
	}

	return *result, nil
}

func (c *clientImpl) GetCompany(ctx context.Context, businessID int64) (*CompanyDTO, error) {
	const path = "/service/business/business/getCompanyByBusinessId"
	values := url.Values{}
	values.Set("businessId", strconv.FormatInt(businessID, 10))

	resp, err := c.cli.Get(ctx, path, values)
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	company, err := httputil.ParseResponse[CompanyDTO](resp)
	if err != nil {
		return nil, err
	}

	return company, nil
}
