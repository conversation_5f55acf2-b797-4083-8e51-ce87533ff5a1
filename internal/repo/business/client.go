package business

import (
	"context"
	"net/url"
	"strconv"

	mhttp "github.com/MoeGolibrary/go-lib/http"
	"github.com/MoeGolibrary/moego-svc-order-v2/internal/repo/internal/httphelper"
)

const (
	TipAllocationTypeAllToStaff = "ALL_TO_STAFF"
	TipAllocationTypeByService  = "BY_SERVICE"
)

//go:generate mockery --name=Client --keeptree --case underscore --with-expecter --output ../../mocks/business
type Client interface {
	GetPayrollSetting(ctx context.Context, bid int64) (*PayrollSetting, error)
}

type PayrollSetting struct {
	ID                     int64  `json:"id"`
	BusinessID             int64  `json:"businessId"`
	CompanyID              int64  `json:"companyId"`
	SplitTipsMethod        int8   `json:"splitTipsMethod"`
	NewPayrollEnanble      bool   `json:"newPayrollEnanble"`
	ServcieCommissionBased int8   `json:"servcieCommissionBased"`
	TipAllocationType      string `json:"tipAllocationType"`
}

type client struct {
	cli mhttp.Client
}

// GetBusinessPayrollSettingByBid implements BusinessClient.
func (b *client) GetPayrollSetting(ctx context.Context, bid int64) (
	*PayrollSetting, error,
) {
	const path = "/service/business/payroll/setting/business"

	values := url.Values{}
	values.Set("businessId", strconv.FormatInt(bid, 10))

	resp, err := b.cli.Get(ctx, path, values)
	if err != nil {
		return nil, err
	}

	defer func() { _ = resp.Body.Close() }()

	return httphelper.ParseResponse[PayrollSetting](resp)
}

func NewBusinessClient() Client {
	cli := mhttp.NewClient("http://moego-service-business:9203")

	return &client{
		cli: cli,
	}
}
