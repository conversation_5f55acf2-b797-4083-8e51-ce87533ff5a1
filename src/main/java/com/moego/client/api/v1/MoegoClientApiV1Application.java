package com.moego.client.api.v1;

import java.util.TimeZone;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@EnableFeignClients({
    "com.moego.server.business.client",
    "com.moego.server.customer.client",
    "com.moego.server.grooming.client",
    "com.moego.server.payment.client",
    "com.moego.server.message.client",
    "com.moego.server.retail.client",
})
public class MoegoClientApiV1Application {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        SpringApplication.run(MoegoClientApiV1Application.class, args);
    }
}
