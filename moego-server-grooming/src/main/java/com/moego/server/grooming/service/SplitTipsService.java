package com.moego.server.grooming.service;

import com.google.common.collect.Lists;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.order.v1.CustomizedTipConfig;
import com.moego.idl.models.order.v1.CustomizedTipType;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.SplitTipsMethod;
import com.moego.idl.models.order.v1.SplitTipsRecord;
import com.moego.idl.service.order.v1.CustomizedTipConfigInput;
import com.moego.idl.service.order.v1.GetSplitTipsInput;
import com.moego.idl.service.order.v1.GetSplitTipsListInput;
import com.moego.idl.service.order.v1.GetSplitTipsListOutput;
import com.moego.idl.service.order.v1.GetSplitTipsOutput;
import com.moego.idl.service.order.v1.SaveSplitTipsInput;
import com.moego.idl.service.order.v2.ListTipsSplitDetailsBySourceRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.server.business.client.IPayrollSettingClient;
import com.moego.server.business.dto.BusinessPayrollSettingDTO;
import com.moego.server.grooming.dto.AmountPercentagePair;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.dto.TipSplitDetailDTO;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapstruct.PetDetailConverter;
import com.moego.server.grooming.params.PreviewSplitTipParams;
import com.moego.server.grooming.service.dto.ReportWebApptPetDetail;
import com.moego.server.grooming.service.dto.report.AppointmentTipsSplitDetail;
import com.moego.server.grooming.service.utils.ReportUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class SplitTipsService {

    @Autowired
    private OrderService orderService;

    @Autowired
    private AppointmentServiceDetailService appointmentDetailService;

    @Autowired
    private IPayrollSettingClient iPayrollSettingClient;

    @Autowired
    private GroomingServiceOperationService groomingServiceOperationService;

    @Autowired
    private com.moego.idl.service.order.v1.SplitTipsServiceGrpc.SplitTipsServiceBlockingStub splitTipsClient;

    @Autowired
    private com.moego.idl.service.order.v2.SplitTipsServiceGrpc.SplitTipsServiceBlockingStub splitTipsClientV2;

    public static final String SPLIT_METHOD_BY_SERVICE = "byService";
    public static final String SPLIT_METHOD_BY_EQUALLY = "byEqually";
    public static final String SPLIT_METHOD_CUSTOMIZED = "customized";

    /**
     * 查询单个订单的tip split记录，同时计算三种split method对应的金额
     *
     * @param businessId
     * @param orderId
     * @return
     */
    public TipSplitDetailDTO getTipSplitDetail(Integer businessId, Long orderId) {
        // 检查订单 sourceType, tipsAmount
        OrderModel order = orderService.getOrderModelById(businessId, orderId.intValue());
        if (!Objects.equals(order.getSourceType(), OrderSourceType.APPOINTMENT.getSource())
                || PrimitiveTypeUtil.isNullOrZero(order.getSourceId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "could not find appointment info");
        }

        if (order.getTipsAmount() <= 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "order tips amount is zero");
        }
        return getTipsSplitDetailForOrder(order);
    }

    /**
     * 预览订单的 Tip split 情况.
     * @param businessId
     * @param params
     * @return
     */
    public TipSplitDetailDTO previewTipSplitDetail(Integer businessId, PreviewSplitTipParams params) {
        // 检查订单 sourceType, tipsAmount
        OrderModel order =
                orderService.getOrderModelById(businessId, params.getOrderId().intValue());
        if (!Objects.equals(order.getSourceType(), OrderSourceType.APPOINTMENT.getSource())
                || PrimitiveTypeUtil.isNullOrZero(order.getSourceId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "could not find appointment info");
        }

        if (order.getTipsAmount() <= 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "order tips amount is zero");
        }

        // 删除的场景，返回纯默认规则计算的结果.
        if (params.isDeleted()) {
            return buildTipSplitDetail(order, null, false);
        }

        // Customized 的情况需要需要按模式计算.
        boolean isCustomized = params.getSplitMethod().equals(SplitTipsMethod.SPLIT_TIPS_METHOD_CUSTOMIZED_VALUE);
        return buildTipSplitDetail(order, params.toSplitRecord(), isCustomized);
    }

    public TipSplitDetailDTO getTipsSplitDetailForOrder(OrderModel order) {
        var businessId = Math.toIntExact(order.getBusinessId());
        var orderId = order.getId();

        // 获取是否有定制配置.
        GetSplitTipsOutput output = splitTipsClient.getSplitTipsRecord(GetSplitTipsInput.newBuilder()
                .setBusinessId(businessId)
                .setOrderId(orderId)
                .build());

        if (output.getExist()) {
            return buildTipSplitDetail(order, output.getSplitTipsRecord(), false);
        }

        return buildTipSplitDetail(order, null, false);
    }

    public void saveTipSplitRecord(SplitTipsRecord splitTipsRecord) {
        if (Objects.equals(splitTipsRecord.getSplitMethod(), SplitTipsMethod.SPLIT_TIPS_METHOD_CUSTOMIZED)) {
            if (splitTipsRecord.getCustomizedType().equals(CustomizedTipType.CUSTOMIZED_TIP_TYPE_UNSPECIFIED)
                    || CollectionUtils.isEmpty(splitTipsRecord.getCustomizedConfigList())) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
            }
        }

        OrderModel order = orderService.getOrderModelById(
                Math.toIntExact(splitTipsRecord.getBusinessId()), Math.toIntExact(splitTipsRecord.getOrderId()));

        // 构造 SplitRecord.
        TipSplitDetailDTO tipSplitDetailDTO = buildTipSplitDetail(order, splitTipsRecord, true);

        SaveSplitTipsInput.Builder inputBuilder = SaveSplitTipsInput.newBuilder()
                .setBusinessId(splitTipsRecord.getBusinessId())
                .setApplyBy(splitTipsRecord.getApplyBy())
                .setOrderId(splitTipsRecord.getOrderId())
                .setSplitMethod(splitTipsRecord.getSplitMethod())
                // svc-order 侧不允许传入 Unspecified 的情况，这了兜底来一个 BY_PERCENTAGE.
                .setCustomizedType(
                        splitTipsRecord.getCustomizedType().equals(CustomizedTipType.CUSTOMIZED_TIP_TYPE_UNSPECIFIED)
                                ? CustomizedTipType.CUSTOMIZED_TIP_TYPE_PERCENTAGE
                                : splitTipsRecord.getCustomizedType());

        if (splitTipsRecord.getIsBusinessTipAmountEffective() && splitTipsRecord.hasBusinessTipAmount()) {
            inputBuilder.setBusinessTipAmount(splitTipsRecord.getBusinessTipAmount());
        }

        if (CollectionUtils.isEmpty(tipSplitDetailDTO.getStaffTipAmountList())) {
            splitTipsClient.saveSplitTipsRecord(inputBuilder.build());
            return;
        }

        for (TipSplitDetailDTO.StaffTipAmountDTO dto : tipSplitDetailDTO.getStaffTipAmountList()) {
            CustomizedTipConfigInput.Builder builder =
                    CustomizedTipConfigInput.newBuilder().setStaffId(dto.getStaffId());

            AmountPercentagePair amountPercentagePair =
                    switch (splitTipsRecord.getSplitMethod()) {
                        case SPLIT_TIPS_METHOD_BY_SERVICE -> dto.getAmountMap().get(SPLIT_METHOD_BY_SERVICE);
                        case SPLIT_TIPS_METHOD_BY_EQUALLY -> dto.getAmountMap().get(SPLIT_METHOD_BY_EQUALLY);
                        case SPLIT_TIPS_METHOD_CUSTOMIZED -> dto.getAmountMap().get(SPLIT_METHOD_CUSTOMIZED);
                        default -> throw ExceptionUtil.bizException(
                                Code.CODE_PARAMS_ERROR,
                                "unsupported split method: " + splitTipsRecord.getSplitMethod());
                    };

            builder.setAmount(amountPercentagePair.getAmount().doubleValue())
                    .setPercentage(amountPercentagePair.getPercentage());

            inputBuilder.addCustomizedConfig(builder.build());
        }

        splitTipsClient.saveSplitTipsRecord(inputBuilder.build());
    }

    private TipSplitDetailDTO buildTipSplitDetail(
            OrderModel order, SplitTipsRecord splitRecord, boolean calculateCustomized) {
        var businessId = Math.toIntExact(order.getBusinessId());
        var orderId = order.getId();
        BigDecimal tipsAmount = BigDecimal.valueOf(order.getTipsAmount());
        Integer appointmentId = (int) order.getSourceId();
        // 过滤order
        boolean isOriginOrder = OrderModel.OrderType.ORIGIN.equals(order.getOrderType());
        var allPetDetails = appointmentDetailService
                .getByAppointmentsAndOrderId(Math.toIntExact(orderId), isOriginOrder, List.of(appointmentId))
                .get(appointmentId);

        List<GroomingPetDetailDTO> petDetails =
                PetDetailConverter.INSTANCE.entityToGroomingPetDetailDTOs(allPetDetails.key());
        List<EvaluationServiceDetailDTO> evaluationServiceDetails =
                appointmentDetailService.toEvaluationServiceDetailDTOListV2(allPetDetails.value());

        getOperation(businessId, Math.toIntExact(order.getSourceId()), petDetails);

        TipSplitDetailDTO result = new TipSplitDetailDTO();
        result.setOrderId(orderId);
        // business payroll 设置 default split tips method
        BusinessPayrollSettingDTO businessPayrollSetting = iPayrollSettingClient.getBusinessPayrollSetting(businessId);
        result.setSplitMethod(
                businessPayrollSetting == null
                        ? SplitTipsMethod.SPLIT_TIPS_METHOD_BY_SERVICE_VALUE
                        : businessPayrollSetting.getSplitTipsMethod().intValue());
        result.setCustomizedType(CustomizedTipType.CUSTOMIZED_TIP_TYPE_PERCENTAGE_VALUE);

        Map<Integer, AmountPercentagePair> customizedTipsSplitMap = new HashMap<>();
        // 先把 Tips 在 Business 和 Staff 之间分清楚.
        // 根据 tipAllocationType 配置计算.
        BusinessPayrollSettingDTO.TipAllocationType tipAllocationType =
                businessPayrollSetting != null && businessPayrollSetting.getTipAllocationType() != null
                        ? businessPayrollSetting.getTipAllocationType()
                        : BusinessPayrollSettingDTO.TipAllocationType.ALL_TO_STAFF; // 默认值

        Pair<BigDecimal, BigDecimal> businessStaffTipAmount = splitTipsBetweenBusinessAndStaff(
                tipsAmount, BigDecimal.valueOf(order.getTipsBasedAmount()), petDetails, evaluationServiceDetails, tipAllocationType);

        BigDecimal businessTipAmount = businessStaffTipAmount.getLeft();
        BigDecimal staffTipAmount = businessStaffTipAmount.getRight();

        // 获取是否有定制配置.
        if (splitRecord != null) {
            result.setSplitMethod(splitRecord.getSplitMethodValue());
            result.setCustomizedType(splitRecord.getCustomizedTypeValue());

            // 有定制过 BusinessTipAmount 的话，覆盖默认值.
            if (splitRecord.getIsBusinessTipAmountEffective()) {
                businessTipAmount =
                        new BigDecimal(splitRecord.getBusinessTipAmount().getValue());
                staffTipAmount = tipsAmount.subtract(businessTipAmount);

                if (businessTipAmount.compareTo(tipsAmount) > 0) {
                    throw ExceptionUtil.bizException(
                            Code.CODE_PARAMS_ERROR, "Business tip amount is greater than order's tips amount.");
                }
            }

            if (!CollectionUtils.isEmpty(splitRecord.getCustomizedConfigList())) {
                if (!calculateCustomized) {
                    customizedTipsSplitMap = getTipSplitCustomized(splitRecord.getCustomizedConfigList());

                } else if (splitRecord.getCustomizedType().equals(CustomizedTipType.CUSTOMIZED_TIP_TYPE_PERCENTAGE)) {
                    customizedTipsSplitMap = splitRecord.getCustomizedConfigList().stream()
                            .collect(Collectors.toMap(it -> Math.toIntExact(it.getStaffId()), it -> {
                                AmountPercentagePair amountPercentagePair = new AmountPercentagePair();
                                amountPercentagePair.setPercentage(it.getPercentage());
                                return amountPercentagePair;
                            }));

                    verifyCustomizedByPercentageParams(staffTipAmount, customizedTipsSplitMap);
                } else {
                    customizedTipsSplitMap = splitRecord.getCustomizedConfigList().stream()
                            .collect(Collectors.toMap(it -> Math.toIntExact(it.getStaffId()), it -> {
                                AmountPercentagePair amountPercentagePair = new AmountPercentagePair();
                                amountPercentagePair.setAmount(BigDecimal.valueOf(it.getAmount()));
                                return amountPercentagePair;
                            }));

                    verifyCustomizedByAmountParams(staffTipAmount, customizedTipsSplitMap);
                }
            }
        }

        result.setBusinessTipAmount(businessTipAmount);

        // 再计算 Staff 层面的三种 split method 的 tips 金额
        List<GroomingPetDetailDTO> petDetailsToAllocate =
                petDetails.stream().filter(p -> p.getStaffId() > 0).collect(Collectors.toList());
        Map<Integer, AmountPercentagePair> serviceTipsSplitMap =
                getTipsSplitByService(staffTipAmount, petDetailsToAllocate);
        Map<Integer, AmountPercentagePair> equallyTipsSplitMap =
                getTipSplitByEqually(staffTipAmount, petDetailsToAllocate);

        Map<Integer, TipSplitDetailDTO.StaffTipAmountDTO> staffTipsMap = new HashMap<>();
        for (var entry : serviceTipsSplitMap.entrySet()) {
            Integer staffId = entry.getKey();
            TipSplitDetailDTO.StaffTipAmountDTO staffTipAmountDTO =
                    staffTipsMap.computeIfAbsent(staffId, dto -> new TipSplitDetailDTO.StaffTipAmountDTO());
            staffTipAmountDTO.setStaffId(staffId);
            Map<String, AmountPercentagePair> amountMap = staffTipAmountDTO.getAmountMap();
            if (amountMap == null) amountMap = new HashMap<>();

            amountMap.put(SPLIT_METHOD_BY_SERVICE, serviceTipsSplitMap.get(staffId));
            amountMap.put(SPLIT_METHOD_BY_EQUALLY, equallyTipsSplitMap.get(staffId));
            amountMap.put(SPLIT_METHOD_CUSTOMIZED, customizedTipsSplitMap.get(staffId));
            staffTipAmountDTO.setAmountMap(amountMap);
        }
        List<TipSplitDetailDTO.StaffTipAmountDTO> staffTipAmountList = new ArrayList<>(staffTipsMap.values());
        staffTipAmountList.sort(Comparator.comparingInt(TipSplitDetailDTO.StaffTipAmountDTO::getStaffId));
        result.setStaffTipAmountList(staffTipAmountList);

        return result;
    }

    /**
     * 校验 by amount 参数
     */
    public void verifyCustomizedByAmountParams(
            BigDecimal splitTipAmount, Map<Integer, AmountPercentagePair> staffAmountMap) {
        BigDecimal totalAmount = staffAmountMap.values().stream()
                .map(AmountPercentagePair::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (splitTipAmount.compareTo(totalAmount) != 0) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Customized staff tips amount is not equal to staff tips amount.");
        }

        // 计算并保存金额对应的百分比
        int tempTotalPercentage = 0;
        int count = 0;
        for (var entry : staffAmountMap.entrySet()) {
            AmountPercentagePair tipConfigParams = entry.getValue();
            int percentage;
            if (count == staffAmountMap.size() - 1) {
                percentage = 100 - tempTotalPercentage;
            } else if (splitTipAmount.compareTo(BigDecimal.ZERO) == 0) {
                // 避免除以 0.
                percentage = 0;
            } else {
                percentage = tipConfigParams
                        .getAmount()
                        .divide(splitTipAmount, 2, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100))
                        .intValue();
                tempTotalPercentage = tempTotalPercentage + percentage;
            }
            tipConfigParams.setPercentage(percentage);
            count++;
        }
    }

    /**
     * 校验 by percentage 参数
     */
    public void verifyCustomizedByPercentageParams(
            BigDecimal splitTipAmount, Map<Integer, AmountPercentagePair> staffAmountMap) {
        Integer totalPercentage = staffAmountMap.values().stream()
                .map(AmountPercentagePair::getPercentage)
                .reduce(0, Integer::sum);

        if (totalPercentage != 100) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Total percentage is not equal to 100%.");
        }

        // 计算并保存百分比对应的金额
        BigDecimal tempTotalAmount = BigDecimal.ZERO;
        int count = 0;
        for (var entry : staffAmountMap.entrySet()) {
            AmountPercentagePair tipConfigParams = entry.getValue();
            BigDecimal amount;
            if (count == staffAmountMap.size() - 1) {
                amount = splitTipAmount.subtract(tempTotalAmount);
            } else {
                amount = splitTipAmount
                        .multiply(BigDecimal.valueOf(tipConfigParams.getPercentage()))
                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                tempTotalAmount = tempTotalAmount.add(amount);
            }
            tipConfigParams.setAmount(amount);
            count++;
        }
    }

    private void getOperation(Integer businessId, Integer groomingId, List<GroomingPetDetailDTO> petDetails) {
        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                groomingServiceOperationService.getOperationMapByGroomingId(businessId, groomingId);
        petDetails.stream()
                .filter(groomingPetDetailDTO -> operationMap.containsKey(groomingPetDetailDTO.getId()))
                .forEach(groomingPetDetailDTO -> {
                    List<GroomingServiceOperationDTO> operationList = operationMap.get(groomingPetDetailDTO.getId());
                    groomingPetDetailDTO.setOperationList(operationList);
                });
    }

    /**
     * report获取order split tips记录
     *
     * @param businessId
     * @param tipsMap Map<orderId, tipAmount>
     * @param petDetailMap key: orderId value: 预约中的 petDetail
     * @param evaluationServiceDetailMap key: orderId value: 预约中的 evaluationDetail
     * @return 外层用orderId作为key，内层用staffId作为key，value是每个staff分到的tips
     */
    public Map<Integer, Map<Integer, BigDecimal>> getOrdersTipSplitMap(
            Integer businessId,
            Map<Integer, BigDecimal> tipsMap,
            Map<Integer, BigDecimal> tipsBaseAmountMap,
            Map<Integer, List<ReportWebApptPetDetail>> petDetailMap,
            Map<Integer, List<EvaluationServiceDetailDTO>> evaluationServiceDetailMap) {
        List<Long> orderIds = tipsMap.keySet().stream().map(Integer::longValue).collect(Collectors.toList());
        Map<Long, SplitTipsRecord> splitTipsRecordMap = getSplitTipsListRecord(businessId, orderIds);
        Map<Long, SplitTipsMethod> orderSplitTipMethod = getSplitTipsMethod(businessId, orderIds, splitTipsRecordMap);

        // 获取 business payroll setting 以获取 tipAllocationType 配置
        BusinessPayrollSettingDTO businessPayrollSetting = iPayrollSettingClient.getBusinessPayrollSetting(businessId);
        BusinessPayrollSettingDTO.TipAllocationType tipAllocationType =
                businessPayrollSetting != null && businessPayrollSetting.getTipAllocationType() != null
                        ? businessPayrollSetting.getTipAllocationType()
                        : BusinessPayrollSettingDTO.TipAllocationType.ALL_TO_STAFF; // 默认值

        Map<Integer, Map<Integer, BigDecimal>> orderTipSplitMap = new HashMap<>();
        for (var entry : tipsMap.entrySet()) {
            Integer orderId = entry.getKey();
            BigDecimal orderTips = tipsMap.get(orderId);

            if (!petDetailMap.containsKey(orderId)) {
                continue;
            }

            var petDetails = PetDetailConverter.INSTANCE.reportBeansToGroomingPetDetailDTOs(petDetailMap.get(orderId));
            var splitTipsRecord = splitTipsRecordMap.get(orderId.longValue());
            var staffTips = BigDecimal.ZERO;
            if (splitTipsRecord != null && splitTipsRecord.getIsBusinessTipAmountEffective()) {
                var businessTips = ReportUtil.toBigDecimal(splitTipsRecord.getBusinessTipAmount());
                if (orderTips.compareTo(businessTips) >= 0) {
                    staffTips = orderTips.subtract(businessTips);
                }
            } else {
                Pair<BigDecimal, BigDecimal> businessStaffTipAmount = splitTipsBetweenBusinessAndStaff(
                        orderTips, tipsBaseAmountMap.get(orderId), petDetails, evaluationServiceDetailMap.get(orderId), tipAllocationType);
                staffTips = businessStaffTipAmount.getRight();
            }

            List<GroomingPetDetailDTO> petDetailsToAllocate =
                    petDetails.stream().filter(p -> p.getStaffId() > 0).collect(Collectors.toList());

            Map<Integer, AmountPercentagePair> staffTipSplitResultMap =
                    switch (orderSplitTipMethod.get(orderId.longValue())) {
                        case SPLIT_TIPS_METHOD_BY_EQUALLY -> getTipSplitByEqually(staffTips, petDetailsToAllocate);
                        case SPLIT_TIPS_METHOD_CUSTOMIZED -> getTipSplitCustomized(
                                splitTipsRecord.getCustomizedConfigList());
                        default -> getTipsSplitByService(staffTips, petDetailsToAllocate);
                    };

            Map<Integer, BigDecimal> staffTipsMap = staffTipSplitResultMap.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey, e -> e.getValue().getAmount()));
            orderTipSplitMap.put(orderId, staffTipsMap);
        }
        return orderTipSplitMap;
    }

    /**
     * 删除订单的split tip记录
     *
     * @param businessId
     * @param orderId
     */
    public void invalidTipSplitRecord(Integer businessId, Integer orderId) {
        splitTipsClient.saveSplitTipsRecord(SaveSplitTipsInput.newBuilder()
                .setBusinessId(businessId)
                .setOrderId(orderId)
                .setIsDeleted(true)
                .build());
    }

    /**
     * 获取split method = by service 的tip分配金额
     * 按staff服务金额所占比例分配，最后一个staff采用倒减的方式，保证总和等于订单的tips
     *
     * @param tipsAmount
     * @param petDetails
     * @return
     */
    public Map<Integer, AmountPercentagePair> getTipsSplitByService(
            BigDecimal tipsAmount, List<GroomingPetDetailDTO> petDetails) {
        BigDecimal totalServiceAmount =
                petDetails.stream().map(GroomingPetDetailDTO::getServicePrice).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 当总的服务金额小于等于0时，平均分配给staff
        if (totalServiceAmount.compareTo(BigDecimal.ZERO) <= 0 || tipsAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return getTipSplitByEqually(tipsAmount, petDetails);
        }

        Map<Integer, AmountPercentagePair> staffTipsMap = new HashMap<>();
        BigDecimal tempTotalTips = BigDecimal.ZERO;
        int tempTotalPercentage = 0;
        for (int i = 0; i < petDetails.size(); i++) {
            GroomingPetDetailDTO petDetail = petDetails.get(i);

            if (i == petDetails.size() - 1) {
                if (!CollectionUtils.isEmpty(petDetail.getOperationList())) {
                    for (int j = 0; j < petDetail.getOperationList().size(); j++) {
                        GroomingServiceOperationDTO op =
                                petDetail.getOperationList().get(j);
                        if (j == petDetail.getOperationList().size() - 1) {
                            BigDecimal serviceTips = tipsAmount.subtract(tempTotalTips);
                            int tipsPercentage = 100 - tempTotalPercentage;
                            addToTipsMap(staffTipsMap, op.getStaffId(), serviceTips, tipsPercentage);
                        } else {
                            BigDecimal serviceTips = tipsAmount
                                    .multiply(op.getPrice())
                                    .divide(totalServiceAmount, 2, RoundingMode.HALF_UP);
                            tempTotalTips = tempTotalTips.add(serviceTips);
                            int tipsPercentage = serviceTips
                                    .divide(tipsAmount, 2, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                                    .intValue();
                            tempTotalPercentage = tempTotalPercentage + tipsPercentage;
                            addToTipsMap(staffTipsMap, op.getStaffId(), serviceTips, tipsPercentage);
                        }
                    }
                    continue;
                }
                BigDecimal serviceTips = tipsAmount.subtract(tempTotalTips);
                int tipsPercentage = 100 - tempTotalPercentage;
                addToTipsMap(staffTipsMap, petDetail.getStaffId(), serviceTips, tipsPercentage);
            } else {
                if (!CollectionUtils.isEmpty(petDetail.getOperationList())) {
                    for (GroomingServiceOperationDTO op : petDetail.getOperationList()) {
                        BigDecimal serviceTips =
                                tipsAmount.multiply(op.getPrice()).divide(totalServiceAmount, 2, RoundingMode.HALF_UP);
                        tempTotalTips = tempTotalTips.add(serviceTips);
                        int tipsPercentage = serviceTips
                                .divide(tipsAmount, 2, RoundingMode.HALF_UP)
                                .multiply(BigDecimal.valueOf(100))
                                .intValue();
                        tempTotalPercentage = tempTotalPercentage + tipsPercentage;
                        addToTipsMap(staffTipsMap, op.getStaffId(), serviceTips, tipsPercentage);
                    }
                    continue;
                }
                BigDecimal serviceTips = tipsAmount
                        .multiply(petDetail.getServicePrice())
                        .divide(totalServiceAmount, 2, RoundingMode.HALF_UP);
                tempTotalTips = tempTotalTips.add(serviceTips);
                int tipsPercentage = serviceTips
                        .divide(tipsAmount, 2, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100))
                        .intValue();
                tempTotalPercentage = tempTotalPercentage + tipsPercentage;
                addToTipsMap(staffTipsMap, petDetail.getStaffId(), serviceTips, tipsPercentage);
            }
        }

        return staffTipsMap;
    }

    /**
     * 获取split method = by equally 的tip分配金额
     * 按staff平均分配，最后一个staff采用倒减的方式，保证总和等于订单的tips
     *
     * @param tipsAmount
     * @param petDetails
     * @return key - staffId, value - 金额/百分比
     */
    public Map<Integer, AmountPercentagePair> getTipSplitByEqually(
            BigDecimal tipsAmount, List<GroomingPetDetailDTO> petDetails) {
        // 统计staff数量
        List<Integer> staffIds = petDetails.stream()
                .flatMap(groomingPetDetailDTO -> {
                    if (CollectionUtils.isEmpty(groomingPetDetailDTO.getOperationList())) {
                        return Stream.of(groomingPetDetailDTO.getStaffId());
                    }
                    return groomingPetDetailDTO.getOperationList().stream()
                            .map(GroomingServiceOperationDTO::getStaffId);
                })
                .distinct()
                .toList();
        long staffCount = staffIds.size();

        Map<Integer, AmountPercentagePair> staffTipsMap = new HashMap<>();
        BigDecimal tempTotalTips = BigDecimal.ZERO;
        int tempTotalPercentage = 0;
        for (int i = 0; i < staffIds.size(); i++) {
            Integer staffId = staffIds.get(i);

            BigDecimal serviceTips;
            int tipsPercentage;
            if (i == staffIds.size() - 1) {
                serviceTips = tipsAmount.subtract(tempTotalTips).setScale(2, RoundingMode.HALF_UP);
                tipsPercentage = 100 - tempTotalPercentage;
            } else {
                serviceTips = tipsAmount.divide(BigDecimal.valueOf(staffCount), 2, RoundingMode.HALF_UP);
                tempTotalTips = tempTotalTips.add(serviceTips);
                tipsPercentage = Double.valueOf(1d / staffCount * 100).intValue();
                tempTotalPercentage = tempTotalPercentage + tipsPercentage;
            }

            addToTipsMap(staffTipsMap, staffId, serviceTips, tipsPercentage);
        }

        return staffTipsMap;
    }

    /**
     * 把tips构建对象并加到map
     *
     * @param staffTipsMap
     * @param staffId
     * @param serviceTips
     * @param tipsPercentage
     */
    private void addToTipsMap(
            Map<Integer, AmountPercentagePair> staffTipsMap,
            Integer staffId,
            BigDecimal serviceTips,
            Integer tipsPercentage) {
        AmountPercentagePair staffTipsPair = staffTipsMap.get(staffId);
        if (staffTipsPair == null) {
            staffTipsPair = new AmountPercentagePair();
            staffTipsPair.setAmount(serviceTips);
            staffTipsPair.setPercentage(tipsPercentage);
        } else {
            // 相同staff合并
            staffTipsPair.setAmount(staffTipsPair.getAmount().add(serviceTips));
            staffTipsPair.setPercentage(staffTipsPair.getPercentage() + tipsPercentage);
        }
        staffTipsMap.put(staffId, staffTipsPair);
    }

    /**
     * 获取customized tip分配金额
     * 直接把数据库的配置信息解析成map，不做另外的计算
     *
     * @param customizedTipList
     * @return key - staffId, value - 金额/百分比
     */
    public Map<Integer, AmountPercentagePair> getTipSplitCustomized(List<CustomizedTipConfig> customizedTipList) {
        Map<Integer, AmountPercentagePair> staffTipsMap = new HashMap<>();
        for (CustomizedTipConfig tipConfig : customizedTipList) {
            AmountPercentagePair pair = new AmountPercentagePair();
            pair.setAmount(BigDecimal.valueOf(tipConfig.getAmount()).setScale(2, RoundingMode.HALF_UP));
            pair.setPercentage(tipConfig.getPercentage());
            staffTipsMap.put((int) tipConfig.getStaffId(), pair);
        }

        return staffTipsMap;
    }

    /**
     * fulfillment tips 全部分配给 staff，business 不分配
     */
    public Pair<BigDecimal, BigDecimal> splitTipsBetweenBusinessAndStaffForFulfillment(
            BigDecimal tipsAmount, BigDecimal tipsBaseAmount) {

        BigDecimal serviceWithStaffAmount = tipsBaseAmount;

        if (serviceWithStaffAmount.compareTo(BigDecimal.ZERO) < 0) {
            serviceWithStaffAmount = BigDecimal.ZERO;
        }

        // 这里将 Tips 分割成两部分：
        // - 无 Staff，该部分默认分给 Business
        // - 有 Staff，该部分默认分给 Staffs
        List<BigDecimal> splitTips =
                ReportUtil.allocateAmountByPrice(tipsAmount, List.of(BigDecimal.ZERO, serviceWithStaffAmount));

        return Pair.of(splitTips.get(0), splitTips.get(1));
    }

    /**
     * 在 Business 和 Staff 之间分配 Tips.
     * 无 Staff 或者 Evaluation 都算作是 Business 的 Tips，其他的都是分给 Staff 的 Tips.
     */
    public Pair<BigDecimal, BigDecimal> splitTipsBetweenBusinessAndStaff(
            BigDecimal tipsAmount,
            BigDecimal tipsBaseAmount,
            List<GroomingPetDetailDTO> petDetails,
            List<EvaluationServiceDetailDTO> evaluations) {

        List<MoeGroomingPetDetail> allPetDetails =
                PetDetailConverter.INSTANCE.groomingPetDetailDTOToEntities(petDetails);
        List<MoeGroomingPetDetail> petDetailsWithoutStaff = allPetDetails.stream()
                .filter(p -> p.getStaffId() == null || p.getStaffId() <= 0)
                .collect(Collectors.toList());
        List<EvaluationServiceDetail> evaluationServiceDetails =
                appointmentDetailService.toEvaluationServiceDetailList(evaluations);

        BigDecimal serviceWithoutStaffAmount = appointmentDetailService.calculateAmount(
                allPetDetails, petDetailsWithoutStaff, evaluationServiceDetails);
        BigDecimal serviceWithStaffAmount = tipsBaseAmount.subtract(serviceWithoutStaffAmount);

        if (serviceWithStaffAmount.compareTo(BigDecimal.ZERO) < 0) {
            serviceWithStaffAmount = BigDecimal.ZERO;
        }

        // 这里将 Tips 分割成两部分：
        // - 无 Staff，该部分默认分给 Business
        // - 有 Staff，该部分默认分给 Staffs
        List<BigDecimal> splitTips = ReportUtil.allocateAmountByPrice(
                tipsAmount, List.of(serviceWithoutStaffAmount, serviceWithStaffAmount));

        return Pair.of(splitTips.get(0), splitTips.get(1));
    }

    /**
     * 在 Business 和 Staff 之间分配 Tips，根据 tipAllocationType 配置.
     */
    public Pair<BigDecimal, BigDecimal> splitTipsBetweenBusinessAndStaff(
            BigDecimal tipsAmount,
            BigDecimal tipsBaseAmount,
            List<GroomingPetDetailDTO> petDetails,
            List<EvaluationServiceDetailDTO> evaluations,
            BusinessPayrollSettingDTO.TipAllocationType tipAllocationType) {

        switch (tipAllocationType) {
            case ALL_TO_STAFF:
                // 所有 tips 都分给 staff
                return Pair.of(BigDecimal.ZERO, tipsAmount);
            case BY_SERVICE:
            default:
                // 按原有逻辑分配：有 staff 的 service 分给 staff，无 staff 的分给 business
                return splitTipsBetweenBusinessAndStaff(tipsAmount, tipsBaseAmount, petDetails, evaluations);
        }
    }

    private Map<Long, SplitTipsRecord> getSplitTipsListRecord(Integer businessId, List<Long> orderIds) {
        GetSplitTipsListOutput output = splitTipsClient.getSplitTipsListRecord(GetSplitTipsListInput.newBuilder()
                .setBusinessId(businessId)
                .addAllOrderId(orderIds)
                .build());
        return output.getSplitTipsRecordList().stream().collect(Collectors.toMap(SplitTipsRecord::getOrderId, r -> r));
    }

    /**
     * @return Map<orderId, SplitTipsMethod>
     */
    private Map<Long, SplitTipsMethod> getSplitTipsMethod(
            Integer businessId, List<Long> orderIds, Map<Long, SplitTipsRecord> splitTipsRecordMap) {
        Map<Long, SplitTipsMethod> result = new HashMap<>();
        if (CollectionUtils.isEmpty(orderIds)) {
            return result;
        }

        SplitTipsMethod defaultMethod = SplitTipsMethod.SPLIT_TIPS_METHOD_BY_SERVICE;

        // business payroll default split tips method
        BusinessPayrollSettingDTO businessPayrollSetting = iPayrollSettingClient.getBusinessPayrollSetting(businessId);
        if (businessPayrollSetting != null) {
            defaultMethod = SplitTipsMethod.forNumber(
                    businessPayrollSetting.getSplitTipsMethod().intValue());
        }

        // 如果有tip split的记录，按照split tip来分配，否则按service price比例分配
        for (Long orderId : orderIds) {
            SplitTipsRecord splitTipsRecord = splitTipsRecordMap.get(orderId);
            if (splitTipsRecord != null) {
                result.put(orderId, splitTipsRecord.getSplitMethod());
            } else {
                result.put(orderId, defaultMethod);
            }
        }
        return result;
    }

    public Map<Integer, AppointmentTipsSplitDetail> getAppointmentTipsSplitMap(List<Integer> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return Map.of();
        }
        Map<Integer, AppointmentTipsSplitDetail> apptTipsSplitMap = new HashMap<>();
        // 单次只能拉 100 个预约的，分批拉取，TODO 预约数量太大的时候，可能会拖慢接口，修改为并发查询
        List<List<Integer>> appointmentIdBatches = Lists.partition(appointmentIds, 100);
        for (List<Integer> appointmentIdBatch : appointmentIdBatches) {
            var res = splitTipsClientV2.listTipsSplitDetailsBySource(ListTipsSplitDetailsBySourceRequest.newBuilder()
                    .addAllSources(appointmentIdBatch.stream()
                            .map(apptId -> ListTipsSplitDetailsBySourceRequest.Source.newBuilder()
                                    .setSourceId(apptId)
                                    .setSourceType(com.moego.idl.models.order.v1.OrderSourceType.APPOINTMENT)
                                    .build())
                            .toList())
                    .build());

            for (var tipsSplitDetail : res.getTipsSplitDetailsList()) {
                var staffTipsMap = tipsSplitDetail.getTipsSplitDetailsList().stream()
                        .collect(Collectors.toMap(
                                d -> (int) d.getStaffId(), d -> MoneyUtils.fromGoogleMoney(d.getSplitAmount())));
                apptTipsSplitMap.put(
                        (int) tipsSplitDetail.getTipsSplit().getSourceId(),
                        new AppointmentTipsSplitDetail(
                                tipsSplitDetail.getTipsSplit().getCollectedOrderIdsList(), staffTipsMap));
            }
        }
        return apptTipsSplitMap;
    }
}
